# Main settings
COMPOSE_PROJECT_NAME=wordpress-fpm
NGINX_SERVER_NAME=wordpress.localhost
WARMUP_URL=http://wordpress.localhost
PMA_SERVER_NAME=pma.localhost

# FrankenWP Configuration
CADDY_GLOBAL_OPTIONS=
CACHE_LOC=/tmp/cache
TTL=3600
PURGE_KEY=
WORDPRESS_CONFIG_EXTRA=
FORCE_HTTPS=true

# Caddy Cache-Handler Configuration
CACHE_MEMORY_LIMIT=64MB
CACHE_DISK_SIZE=512MB
BYPASS_PATH_PATTERNS=
CACHE_RESPONSE_CODES=200,404,405

# MYSQL settings
MYSQL_ROOT_PASSWORD=root
MYSQL_HOST="db-${COMPOSE_PROJECT_NAME}"
MYSQL_DATABASE=wordpress
#MYSQL_DATABASE=wordpress_bk
MYSQL_USER=wordpress
MYSQL_PASSWORD=wordpress
# Wordpress settings
MYSQL_TABLE_PREFIX=wpsn_
#MYSQL_TABLE_PREFIX=wp_

# Backup settings
BACKUP_DIR=backups
GPG_RECIPIENT=backup@flash
LOCAL_BACKUP_RETENTION_DAYS=21
RCLONE_REMOTE_1=webh:backups:10

# PMA Captcha settings
CAPTCHA_LOGIN_PUBLIC_KEY=your-public-key
CAPTCHA_LOGIN_PRIVATE_KEY=your-private-key
