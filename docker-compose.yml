services:
  wp:
    build:
      context: docker/frankenwp
      dockerfile: Dockerfile
    expose:
      - 80
    environment:
      # FrankenPHP Configuration
      SERVER_NAME: ${SERVER_NAME}:80
      CADDY_GLOBAL_OPTIONS: |
        auto_https off

      # WordPress Database Configuration
      WORDPRESS_DB_HOST: db-${COMPOSE_PROJECT_NAME}
      WORDPRESS_DB_USER: ${MYSQL_USER:-}
      WORDPRESS_DB_PASSWORD: ${MYSQL_PASSWORD:-}
      WORDPRESS_DB_NAME: ${MYSQL_DATABASE:-}
      WORDPRESS_TABLE_PREFIX: ${MYSQL_TABLE_PREFIX:-}
      WORDPRESS_CONFIG_EXTRA: |
        define('DISABLE_WP_CRON', true);
      
      # Nginx Proxy Integration
      VIRTUAL_HOST: ${SERVER_NAME}
      LETSENCRYPT_HOST: ${SERVER_NAME}
    hostname: wp-${COMPOSE_PROJECT_NAME}
    container_name: wp-${COMPOSE_PROJECT_NAME}
    labels:
      - "ofelia.enabled=true"
      - "ofelia.job-exec.wp-cron-job-${COMPOSE_PROJECT_NAME}.schedule=@every 5m"
      - "ofelia.job-exec.wp-cron-job-${COMPOSE_PROJECT_NAME}.command=sudo -E -u www-data /usr/local/bin/wp cron event run --due-now"
      - "ofelia.job-exec.warmup-cache-${COMPOSE_PROJECT_NAME}.schedule=@every 1h"
      - "ofelia.job-exec.warmup-cache-${COMPOSE_PROJECT_NAME}.command=/usr/local/bin/warmup-cache.sh ${WARMUP_URL:-${SERVER_NAME}}"
    volumes:
      - wordpress:/var/www/html
      - badger_cache:/var/cache/badger
    restart: unless-stopped
    networks:
      - nginx-proxy
      - default
    depends_on:
      - db
    tty: true

  # DB Service
  db:
    image: mariadb:10.5
    # Set max_allowed_packet to 256M
    command: --max_allowed_packet=268435456
    environment:
        MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
        MYSQL_DATABASE: ${MYSQL_DATABASE}
        MYSQL_USER: ${MYSQL_USER}
        MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    hostname: db-${COMPOSE_PROJECT_NAME}
    container_name: db-${COMPOSE_PROJECT_NAME}
    volumes:
        - db:/var/lib/mysql
    restart: unless-stopped
    networks:
      - default

#  redis:
#    build:
#      context: .
#      dockerfile: docker/redis/Dockerfile
#    environment:
#      REDIS_ARGS: "--requirepass ${REDIS_PASSWORD}"
#    hostname: redis-${COMPOSE_PROJECT_NAME}
#    container_name: redis-${COMPOSE_PROJECT_NAME}
#    restart: unless-stopped
#    networks:
#      - default

  # PMA Service
  pma:
    build:
      context: .
      dockerfile: docker/phpmyadmin/Dockerfile
    expose:
      - 80
    environment:
      VIRTUAL_HOST: ${PMA_SERVER_NAME}
      LETSENCRYPT_HOST: ${PMA_SERVER_NAME}
      NGINX_SERVER_NAME: ${PMA_SERVER_NAME}
      PMA_HOST: db-${COMPOSE_PROJECT_NAME}
      ALLOW_ROOT: ${ALLOW_ROOT:-}
      CAPTCHA_LOGIN_PUBLIC_KEY: ${CAPTCHA_LOGIN_PUBLIC_KEY:-}
      CAPTCHA_LOGIN_PRIVATE_KEY: ${CAPTCHA_LOGIN_PRIVATE_KEY:-}
      CAPTCHA_METHOD: ${CAPTCHA_METHOD:-}
    hostname: pma-${COMPOSE_PROJECT_NAME}
    container_name: pma-${COMPOSE_PROJECT_NAME}
    restart: unless-stopped
    networks:
      - default
      - nginx-proxy
    profiles:
      - pma
    depends_on:
    - db

# Volumes
volumes:
  wordpress:
  db:
  badger_cache:

networks:
  nginx-proxy:
    name: nginx-proxy
    external: true

