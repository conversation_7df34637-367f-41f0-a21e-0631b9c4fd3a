#!/usr/bin/env bash
BATCH_MODE=false
if [ "$1" == "--batch" ]; then
  BATCH_MODE=true
  shift
fi

if [ $# -ne 1 ]; then
  [ "$BATCH_MODE" == "false" ] && echo "Usage: restore-wp-content <tar.gz file name>"
  exit 1
fi

set -o allexport
source ./.env
set +o allexport

[ "$BATCH_MODE" == "false" ] && echo "Restoring wp-content directory ..."

# Ask for confirmation if not in batch mode
if [ "$BATCH_MODE" == "false" ]; then
  read -p "This will remove all files in /var/www/html/wp-content/. Are you sure? (y/N) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled"
    exit 1
  fi
fi

# Clear the wp-content directory first
docker compose exec wp rm -rf /var/www/html/wp-content/*
# Then extract only wp-content from the archive
docker compose exec -T wp tar xz -C /var/www/html --strip-components=1 --wildcards "*/wp-content/*" < "${1}"
status=$?

[ "$BATCH_MODE" == "false" ] && [ $status -eq 0 ] && echo "wp-content directory restored"

exit $status
