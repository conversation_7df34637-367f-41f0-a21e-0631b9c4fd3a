#!/usr/bin/env bash
BATCH_MODE=false
if [ "$1" == "--batch" ]; then
  BATCH_MODE=true
  shift
fi

if [ $# -ne 2 ]; then
  [ "$BATCH_MODE" == "false" ] && echo "Usage: dump-database [--batch] <database_name> <database_dump_file_path>"
  exit 1
fi

set -o allexport
source ./.env
set +o allexport

[ "$BATCH_MODE" == "false" ] && echo "Dumping ${1} database ..."
docker compose exec db mysqldump --add-drop-table -R -u root --password=${MYSQL_ROOT_PASSWORD} ${1} >"${2}"
status=$?

if [ "$BATCH_MODE" == "false" ]; then
  if [ $status -eq 0 ]; then
    echo "Database dumped"
  else
    echo "Dump error. Check dump file for details."
  fi
fi

exit $status
