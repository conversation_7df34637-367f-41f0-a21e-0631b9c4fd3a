#!/usr/bin/env bash

set -o allexport
source ./.env
set +o allexport

# Validate necessary environment variables
required_vars=("MYSQL_DATABASE" "GPG_RECIPIENT" "BACKUP_DIR" "LOCAL_BACKUP_RETENTION_DAYS")
for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "Error: $var is not set in the environment."
    exit 1
  fi
done

NOW=$(date +%Y%m%d%H%M%S)
SQL_BACKUP=${NOW}_database.sql.gz.gpg
FILES_BACKUP=${NOW}_files.tar.gz.gpg

# Backup database
bin/dump-database.sh --batch "${MYSQL_DATABASE}" /dev/stdout | gzip --stdout \
| gpg --batch --trust-model always --encrypt --recipient "${GPG_RECIPIENT}" --output "${BACKUP_DIR}/${SQL_BACKUP}"

# Backup Wordpress public directory
bin/backup-wp-dir.sh --batch /dev/stdout \
| gpg --batch --trust-model always --encrypt --recipient "${GPG_RECIPIENT}" --output "${BACKUP_DIR}/${FILES_BACKUP}"

# Remove old backup files
bin/rm-old-backups.sh "${BACKUP_DIR}" "${LOCAL_BACKUP_RETENTION_DAYS}"

# loop through all rclone envs beginning with RCLONE_REMOTE_1, RCLONE_REMOTE_2 ...
# and extract from their values remote name, remote path and retention days. Values are separated by a colon.
for i in $(env | grep ^RCLONE_REMOTE_ | sort | awk -F= '{print $2}')
do
  remote_name=$(echo $i | cut -d: -f1)
  remote_path=$(echo $i | cut -d: -f2)
  remote_retention_days=$(echo $i | cut -d: -f3)


  # Check if all necessary values are present
  if [ -z "$remote_name" ] || [ -z "$remote_path" ] || [ -z "$remote_retention_days" ]; then
    echo "Error: entry '$i' is missing one or more required values (remote name, remote path, retention days)."
    exit 1
  fi

  rclone copy "${BACKUP_DIR}/${SQL_BACKUP}" "${remote_name}:${remote_path}"
  rclone copy "${BACKUP_DIR}/${FILES_BACKUP}" "${remote_name}:${remote_path}"
  rclone delete "${remote_name}:${remote_path}" --min-age "${remote_retention_days}d"
done
