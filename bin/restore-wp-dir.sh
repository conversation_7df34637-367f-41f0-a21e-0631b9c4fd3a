#!/usr/bin/env bash
BATCH_MODE=false
if [ "$1" == "--batch" ]; then
  BATCH_MODE=true
  shift
fi

if [ $# -ne 1 ]; then
  [ "$BATCH_MODE" == "false" ] && echo "Usage: restore-wp-dir <tar.gz file name>"
  exit 1
fi

set -o allexport
source ./.env
set +o allexport

[ "$BATCH_MODE" == "false" ] && echo "Restoring Wordpress public directory ..."

# Ask for confirmation if not in batch mode
if [ "$BATCH_MODE" == "false" ]; then
  read -p "This will remove all files in /var/www/html/. Are you sure? (y/N) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled"
    exit 1
  fi
fi

# Clear the html directory first
docker compose exec wp rm -rf /var/www/html/*
# Then extract the archive
docker compose exec -T wp tar xz -C /var/www < "${1}"
status=$?

[ "$BATCH_MODE" == "false" ] && [ $status -eq 0 ] && echo "Wordpress public directory restored"

exit $status
