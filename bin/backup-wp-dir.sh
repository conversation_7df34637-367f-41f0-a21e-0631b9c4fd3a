#!/usr/bin/env bash
BATCH_MODE=false
if [ "$1" == "--batch" ]; then
  BATCH_MODE=true
  shift
fi

if [ $# -ne 1 ]; then
  [ "$BATCH_MODE" == "false" ] && echo "Usage: tar-wp-dir <tar.gz file name>"
  exit 1
fi

set -o allexport
source ./.env
set +o allexport

[ "$BATCH_MODE" == "false" ] && echo "Compressing Wordpress public directory ..."

docker compose exec wp tar cz -C /var/www html > "${1}"
status=$?

[ "$BATCH_MODE" == "false" ] && [ $status -eq 0 ] && echo "Wordpress public directory compressed"

exit $status
