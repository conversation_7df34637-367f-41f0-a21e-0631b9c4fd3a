#!/usr/bin/env bash
if [ $# -ne 2 ]
  then
    echo "Usage:"
    echo "search-database.sh <database_name> <search_string>"
    exit 1
fi

set -o allexport
source ./.env
set +o allexport

echo "Searching for '$2' in database '$1'..."
docker compose exec db bash -c "mysql -u root --password=\"${MYSQL_ROOT_PASSWORD}\" -e \"
SELECT table_name, column_name
FROM information_schema.columns
WHERE table_schema = '$1'
AND data_type IN ('varchar', 'char', 'text', 'longtext', 'mediumtext')
\" | tail -n +2 | while read table column; do
  echo \"Searching \$table.\$column...\"
  count=\$(mysql -u root --password=\"${MYSQL_ROOT_PASSWORD}\" -e \"SELECT COUNT(*) FROM $1.\$table WHERE \$column LIKE '%$2%'\" | tail -n +2)
  if [ \"\$count\" -gt 0 ]; then
    echo \"Found \$count matches in \$table.\$column\"
    mysql -u root --password=\"${MYSQL_ROOT_PASSWORD}\" -e \"SELECT * FROM $1.\$table WHERE \$column LIKE '%$2%' LIMIT 5\"
  fi
done"