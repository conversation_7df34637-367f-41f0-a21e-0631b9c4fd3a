#!/bin/bash

# WordPress Domain Migration Script
# Migrates a WordPress installation from one domain to another within Docker Compose environment
# Usage: ./wp-domain-migrate.sh [OPTIONS] <old_domain> <new_domain>

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/wp-domain-migrate-$(date +%Y%m%d_%H%M%S).log"
BACKUP_DIR="$PROJECT_ROOT/backups"

# Default options
DRY_RUN=false
FORCE=false
SKIP_BACKUP=false
VERBOSE=false
HELP=false

# Command shortcuts
WP_CLI="docker compose exec -T -u www-data wp wp"
DOCKER_WP="docker compose exec -T wp"
DOCKER_DB="docker compose exec -T db"
DOCKER_REDIS="docker compose exec -T redis"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Ensure log directory exists
    mkdir -p "$(dirname "$LOG_FILE")" 2>/dev/null || true
    
    case "$level" in
        ERROR)
            echo -e "${RED}[ERROR]${NC} $message" >&2
            echo "[$timestamp] [ERROR] $message" >> "$LOG_FILE" 2>/dev/null || true
            ;;
        WARN)
            echo -e "${YELLOW}[WARN]${NC} $message" >&2
            echo "[$timestamp] [WARN] $message" >> "$LOG_FILE" 2>/dev/null || true
            ;;
        INFO)
            echo -e "${GREEN}[INFO]${NC} $message"
            echo "[$timestamp] [INFO] $message" >> "$LOG_FILE" 2>/dev/null || true
            ;;
        DEBUG)
            if [[ "$VERBOSE" == "true" ]]; then
                echo -e "${BLUE}[DEBUG]${NC} $message"
            fi
            echo "[$timestamp] [DEBUG] $message" >> "$LOG_FILE" 2>/dev/null || true
            ;;
    esac
}

# Help function
show_help() {
    cat << EOF
WordPress Domain Migration Script

USAGE:
    $0 [OPTIONS] <old_domain> <new_domain>

DESCRIPTION:
    Migrates a WordPress installation from one domain to another within a 
    Docker Compose environment using FrankenWP (FrankenPHP + Caddy).
    
    This script handles:
    - Database URL replacement using WP-CLI
    - WordPress configuration file updates
    - Docker environment variable updates
    - Cache clearing (Redis + FrankenWP)
    - SSL certificate regeneration
    - Post-migration verification

OPTIONS:
    -d, --dry-run       Preview changes without executing them
    -f, --force         Skip confirmation prompts
    -s, --skip-backup   Skip pre-migration backup (not recommended)
    -v, --verbose       Enable verbose output
    -h, --help          Show this help message

ARGUMENTS:
    old_domain          Source domain to migrate from (e.g., old-site.com)
    new_domain          Target domain to migrate to (e.g., new-site.com)

EXAMPLES:
    # Basic migration with backup and confirmation
    $0 old-site.com new-site.com
    
    # Dry run to preview changes
    $0 --dry-run old-site.com new-site.com
    
    # Force migration without prompts
    $0 --force old-site.com new-site.com
    
    # Skip backup (not recommended for production)
    $0 --skip-backup old-site.com new-site.com

REQUIREMENTS:
    - Docker Compose environment running
    - WP-CLI available in WordPress container
    - nginx-proxy network configured
    - Database backup utilities available

SAFETY FEATURES:
    - Automatic backup before migration
    - Dry run mode for testing
    - Detailed logging of all operations
    - Rollback capability via backups
    - Emergency recovery options

IMPORTANT NOTES:
    - This script is for SINGLE-SITE WordPress installations only
    - WordPress Multisite networks are not supported
    - Always test on staging environment first
    - Ensure DNS is configured for new domain before migration
    - SSL certificates will be automatically generated for new domain

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -s|--skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                HELP=true
                shift
                ;;
            -*)
                log ERROR "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "${OLD_DOMAIN:-}" ]]; then
                    OLD_DOMAIN="$1"
                elif [[ -z "${NEW_DOMAIN:-}" ]]; then
                    NEW_DOMAIN="$1"
                else
                    log ERROR "Too many arguments: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done

    if [[ "$HELP" == "true" ]]; then
        show_help
        exit 0
    fi

    if [[ -z "${OLD_DOMAIN:-}" ]] || [[ -z "${NEW_DOMAIN:-}" ]]; then
        log ERROR "Both old_domain and new_domain are required"
        show_help
        exit 1
    fi
}

# Validate domain format
validate_domain() {
    local domain="$1"
    local domain_regex='^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$'
    
    if [[ ! "$domain" =~ $domain_regex ]]; then
        log ERROR "Invalid domain format: $domain"
        return 1
    fi
    
    if [[ ${#domain} -gt 253 ]]; then
        log ERROR "Domain too long: $domain (max 253 characters)"
        return 1
    fi
    
    return 0
}

# Initialize script environment
initialize() {
    log INFO "Initializing WordPress domain migration script"
    log INFO "Old domain: $OLD_DOMAIN"
    log INFO "New domain: $NEW_DOMAIN"
    log INFO "Dry run: $DRY_RUN"
    log INFO "Log file: $LOG_FILE"
    
    # Create necessary directories
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$BACKUP_DIR"
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Validate domains
    validate_domain "$OLD_DOMAIN" || exit 1
    validate_domain "$NEW_DOMAIN" || exit 1
    
    # Check if domains are the same
    if [[ "$OLD_DOMAIN" == "$NEW_DOMAIN" ]]; then
        log ERROR "Old and new domains cannot be the same"
        exit 1
    fi
    
    log INFO "Domain validation passed"
}

# Environment verification functions
check_docker_compose() {
    log DEBUG "Checking Docker Compose availability"
    
    if ! command -v docker &> /dev/null; then
        log ERROR "Docker is not installed or not in PATH"
        return 1
    fi
    
    if ! docker compose version &> /dev/null; then
        log ERROR "Docker Compose is not available"
        return 1
    fi
    
    log DEBUG "Docker Compose is available"
    return 0
}

check_containers_running() {
    log DEBUG "Checking if required containers are running"
    
    # Load environment variables
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        # shellcheck disable=SC1091
        source "$PROJECT_ROOT/.env"
    else
        log ERROR ".env file not found in project root"
        return 1
    fi
    
    local compose_name="${COMPOSE_PROJECT_NAME:-wordpress}"
    local wp_container="wp-${compose_name}"
    local db_container="db-${compose_name}"
    
    # Check WordPress container
    if ! docker compose ps --services --filter "status=running" | grep -q "^wp$"; then
        log ERROR "WordPress container ($wp_container) is not running"
        return 1
    fi
    
    # Check database container
    if ! docker compose ps --services --filter "status=running" | grep -q "^db$"; then
        log ERROR "Database container ($db_container) is not running"
        return 1
    fi
    
    log DEBUG "Required containers are running"
    return 0
}

check_wp_cli() {
    log DEBUG "Checking WP-CLI availability in WordPress container"
    
    if ! $DOCKER_WP wp --info &> /dev/null; then
        log ERROR "WP-CLI is not available in WordPress container"
        return 1
    fi
    
    log DEBUG "WP-CLI is available and working"
    return 0
}

check_database_connection() {
    log DEBUG "Checking database connection"
    
    if ! $DOCKER_DB mysql -u "${MYSQL_USER:-wordpress}" -p"${MYSQL_PASSWORD}" -e "SELECT 1" "${MYSQL_DATABASE:-wordpress}" &> /dev/null; then
        log ERROR "Cannot connect to WordPress database"
        return 1
    fi
    
    log DEBUG "Database connection successful"
    return 0
}

check_nginx_proxy_network() {
    log DEBUG "Checking nginx-proxy network"
    
    if ! docker network ls | grep -q "nginx-proxy"; then
        log WARN "nginx-proxy network not found - SSL certificates may not work"
        return 1
    fi
    
    # Check if WordPress container is connected to nginx-proxy network
    local compose_name="${COMPOSE_PROJECT_NAME:-wordpress}"
    local wp_container="wp-${compose_name}"
    
    if ! docker network inspect nginx-proxy --format '{{range .Containers}}{{.Name}} {{end}}' | grep -q "$wp_container"; then
        log WARN "WordPress container not connected to nginx-proxy network"
        return 1
    fi
    
    log DEBUG "nginx-proxy network is properly configured"
    return 0
}

check_old_domain_exists() {
    log DEBUG "Checking if old domain exists in WordPress database"
    
    local site_url
    site_url=$($WP_CLI option get siteurl 2>/dev/null || echo "")
    
    if [[ -z "$site_url" ]]; then
        log ERROR "Could not retrieve WordPress site URL from database"
        return 1
    fi
    
    if [[ "$site_url" != *"$OLD_DOMAIN"* ]]; then
        log WARN "Old domain '$OLD_DOMAIN' not found in current site URL: $site_url"
        if [[ "$FORCE" != "true" ]]; then
            echo -n "Continue anyway? [y/N]: "
            read -r response
            if [[ ! "$response" =~ ^[Yy]$ ]]; then
                log INFO "Migration cancelled by user"
                exit 0
            fi
        fi
    fi
    
    log DEBUG "Old domain validation completed"
    return 0
}

verify_environment() {
    log INFO "Starting environment verification"
    
    local checks=(
        "check_docker_compose"
        "check_containers_running"
        "check_wp_cli" 
        "check_database_connection"
        "check_nginx_proxy_network"
        "check_old_domain_exists"
    )
    
    local failed_checks=0
    
    for check in "${checks[@]}"; do
        if ! $check; then
            ((failed_checks++))
        fi
    done
    
    if [[ $failed_checks -gt 0 ]]; then
        log ERROR "Environment verification failed ($failed_checks checks failed)"
        if [[ "$FORCE" != "true" ]]; then
            log ERROR "Use --force to bypass verification (not recommended)"
            exit 1
        else
            log WARN "Continuing with --force flag despite failed checks"
        fi
    fi
    
    log INFO "Environment verification completed successfully"
    return 0
}

# Backup functions
create_backup() {
    if [[ "$SKIP_BACKUP" == "true" ]]; then
        log INFO "Skipping pre-migration backup as requested"
        return 0
    fi
    
    log INFO "Creating pre-migration backup"
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_prefix="pre-migration-${OLD_DOMAIN//[^a-zA-Z0-9]/_}-to-${NEW_DOMAIN//[^a-zA-Z0-9]/_}"
    local db_backup="${BACKUP_DIR}/${backup_prefix}_db_${timestamp}.sql"
    local files_backup="${BACKUP_DIR}/${backup_prefix}_files_${timestamp}.tar.gz"
    
    # Load environment variables for database name
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        # shellcheck disable=SC1091
        source "$PROJECT_ROOT/.env"
    fi
    
    local db_name="${MYSQL_DATABASE:-wordpress}"
    
    log INFO "Creating database backup: $(basename "$db_backup")"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "[DRY RUN] Would create database backup: $db_backup"
        log INFO "[DRY RUN] Would create files backup: $files_backup"
        return 0
    fi
    
    # Create database backup using existing script
    if [[ -f "$PROJECT_ROOT/bin/dump-database.sh" ]]; then
        log DEBUG "Using existing dump-database.sh script"
        if ! "$PROJECT_ROOT/bin/dump-database.sh" --batch "$db_name" "$db_backup"; then
            log ERROR "Database backup failed"
            return 1
        fi
    else
        log DEBUG "Using docker compose exec for database backup"
        if ! $DOCKER_DB mysqldump \
            -u "${MYSQL_USER:-wordpress}" \
            -p"${MYSQL_PASSWORD}" \
            --routines --triggers --single-transaction \
            "$db_name" > "$db_backup"; then
            log ERROR "Database backup failed"
            return 1
        fi
    fi
    
    log INFO "Creating WordPress files backup: $(basename "$files_backup")"
    
    # Create files backup using existing script if available
    if [[ -f "$PROJECT_ROOT/bin/backup-wp-dir.sh" ]]; then
        log DEBUG "Using existing backup-wp-dir.sh script"
        if ! "$PROJECT_ROOT/bin/backup-wp-dir.sh" "$files_backup"; then
            log ERROR "Files backup failed"
            return 1
        fi
    else
        log DEBUG "Using docker compose exec for files backup"
        if ! $DOCKER_WP tar -czf "/tmp/wp-backup.tar.gz" -C /var/www/html .; then
            log ERROR "Files backup creation failed"
            return 1
        fi
        
        # Copy backup from container to host
        if ! docker compose cp "wp:/tmp/wp-backup.tar.gz" "$files_backup"; then
            log ERROR "Files backup copy failed"
            return 1
        fi
        
        # Clean up temporary file in container
        docker compose exec -T wp rm -f "/tmp/wp-backup.tar.gz" || true
    fi
    
    # Verify backups were created
    if [[ ! -f "$db_backup" ]] || [[ ! -s "$db_backup" ]]; then
        log ERROR "Database backup verification failed"
        return 1
    fi
    
    if [[ ! -f "$files_backup" ]] || [[ ! -s "$files_backup" ]]; then
        log ERROR "Files backup verification failed"
        return 1
    fi
    
    # Store backup paths for potential rollback
    BACKUP_DB_FILE="$db_backup"
    BACKUP_FILES_FILE="$files_backup"
    
    log INFO "Pre-migration backup completed successfully"
    log INFO "Database backup: $db_backup"
    log INFO "Files backup: $files_backup"
    
    # Calculate backup sizes
    local db_size=$(du -h "$db_backup" | cut -f1)
    local files_size=$(du -h "$files_backup" | cut -f1)
    log INFO "Backup sizes - Database: $db_size, Files: $files_size"
    
    return 0
}

# Database migration functions
migrate_database() {
    log INFO "Starting database domain migration"
    log INFO "Replacing '$OLD_DOMAIN' with '$NEW_DOMAIN' in WordPress database"
    
    # Prepare old and new URLs with protocols
    local old_http="http://$OLD_DOMAIN"
    local old_https="https://$OLD_DOMAIN" 
    local new_http="http://$NEW_DOMAIN"
    local new_https="https://$NEW_DOMAIN"
    
    # Detect current protocol
    local current_siteurl
    current_siteurl=$($WP_CLI option get siteurl 2>/dev/null || echo "")
    local target_protocol="https"
    
    if [[ "$current_siteurl" == http://* ]]; then
        target_protocol="http"
        log DEBUG "Detected HTTP site, will migrate to HTTP"
    else
        log DEBUG "Detected HTTPS site, will migrate to HTTPS"
    fi
    
    local old_url new_url
    if [[ "$target_protocol" == "https" ]]; then
        old_url="$old_https"
        new_url="$new_https"
    else
        old_url="$old_http"  
        new_url="$new_http"
    fi
    
    log INFO "Migrating from: $old_url"
    log INFO "Migrating to: $new_url"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "[DRY RUN] Would perform database search-replace operation:"
        log INFO "[DRY RUN] - Replace '$OLD_DOMAIN' with '$NEW_DOMAIN' (single replacement to avoid double-replacement issues)"
        return 0
    fi
    
    # Use WP-CLI search-replace for safe serialized data handling
    if ! $WP_CLI search-replace \
        "$OLD_DOMAIN" "$NEW_DOMAIN" \
        --skip-columns=guid \
        --report-changed-only 2>&1 | tee -a "$LOG_FILE"; then
        log ERROR "Domain replacement failed"
        return 1
    fi
    
    # Update core WordPress options explicitly
    log INFO "Updating core WordPress options"
    
    if ! $WP_CLI option update home "$new_url"; then
        log ERROR "Failed to update home URL"
        return 1
    fi
    
    if ! $WP_CLI option update siteurl "$new_url"; then
        log ERROR "Failed to update site URL"
        return 1
    fi
    
    # Flush WordPress rewrite rules
    log INFO "Flushing WordPress rewrite rules"
    $WP_CLI rewrite flush || log WARN "Failed to flush rewrite rules"
    
    # Verify database changes
    local new_home new_siteurl
    new_home=$($WP_CLI option get home 2>/dev/null || echo "")
    new_siteurl=$($WP_CLI option get siteurl 2>/dev/null || echo "")
    
    if [[ "$new_home" != "$new_url" ]] || [[ "$new_siteurl" != "$new_url" ]]; then
        log ERROR "Database verification failed after migration"
        log ERROR "Expected: $new_url"
        log ERROR "Home URL: $new_home"  
        log ERROR "Site URL: $new_siteurl"
        return 1
    fi
    
    log INFO "Database domain migration completed successfully"
    log INFO "Home URL: $new_home"
    log INFO "Site URL: $new_siteurl"
    
    return 0
}

# File system update functions
update_wp_config() {
    log INFO "Checking wp-config.php for domain-specific constants"
    
    local wp_config_path="/var/www/html/wp-config.php"
    local temp_config="/tmp/wp-config-updated.php"
    
    # Check if wp-config.php exists in container
    if ! docker compose exec -T wp test -f "$wp_config_path"; then
        log WARN "wp-config.php not found in container, skipping wp-config updates"
        return 0
    fi
    
    # Check for domain-specific constants in wp-config.php
    local has_wp_home has_wp_siteurl has_domain_constants
    has_wp_home=$(docker compose exec -T wp grep "define.*WP_HOME" "$wp_config_path" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    has_wp_siteurl=$(docker compose exec -T wp grep "define.*WP_SITEURL" "$wp_config_path" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    has_domain_constants=$(docker compose exec -T wp grep "$OLD_DOMAIN" "$wp_config_path" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    
    if [[ "$has_wp_home" -eq 0 && "$has_wp_siteurl" -eq 0 && "$has_domain_constants" -eq 0 ]]; then
        log INFO "No domain-specific constants found in wp-config.php, skipping"
        return 0
    fi
    
    log INFO "Found domain references in wp-config.php, updating..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "[DRY RUN] Would update wp-config.php:"
        if [[ "$has_wp_home" -gt 0 ]]; then
            log INFO "[DRY RUN] - Update WP_HOME constant"
        fi
        if [[ "$has_wp_siteurl" -gt 0 ]]; then
            log INFO "[DRY RUN] - Update WP_SITEURL constant"
        fi
        if [[ "$has_domain_constants" -gt 0 ]]; then
            log INFO "[DRY RUN] - Replace domain references: $OLD_DOMAIN -> $NEW_DOMAIN"
        fi
        return 0
    fi
    
    # Copy wp-config.php from container to temporary location for editing
    if ! docker compose exec -T wp cp "$wp_config_path" "$temp_config"; then
        log ERROR "Failed to copy wp-config.php for editing"
        return 1
    fi
    
    # Determine target protocol based on current database settings
    local target_protocol="https"
    local current_siteurl
    current_siteurl=$($WP_CLI option get siteurl 2>/dev/null || echo "")
    
    if [[ "$current_siteurl" == http://* ]]; then
        target_protocol="http"
    fi
    
    local new_url="${target_protocol}://${NEW_DOMAIN}"
    
    # Update wp-config.php constants using sed
    log INFO "Updating WP_HOME and WP_SITEURL constants"
    
    # Update or add WP_HOME constant
    if [[ "$has_wp_home" -gt 0 ]]; then
        docker compose exec -T wp sed -i "s|define.*WP_HOME.*|define('WP_HOME', '$new_url');|g" "$temp_config"
    fi
    
    # Update or add WP_SITEURL constant  
    if [[ "$has_wp_siteurl" -gt 0 ]]; then
        docker compose exec -T wp sed -i "s|define.*WP_SITEURL.*|define('WP_SITEURL', '$new_url');|g" "$temp_config"
    fi
    
    # Replace any other domain references (avoid double replacement)
    docker compose exec -T wp sed -i "s|\\b$OLD_DOMAIN\\b|$NEW_DOMAIN|g" "$temp_config"
    
    # Copy updated config back
    if ! docker compose exec -T wp cp "$temp_config" "$wp_config_path"; then
        log ERROR "Failed to update wp-config.php"
        return 1
    fi
    
    # Clean up temporary file
    docker compose exec -T wp rm -f "$temp_config" || true
    
    log INFO "wp-config.php updated successfully"
    return 0
}

update_htaccess() {
    log INFO "Checking .htaccess for domain-specific rules"
    
    local htaccess_path="/var/www/html/.htaccess"
    local temp_htaccess="/tmp/htaccess-updated"
    
    # Check if .htaccess exists in container
    if ! docker compose exec -T wp test -f "$htaccess_path"; then
        log INFO ".htaccess not found, skipping .htaccess updates"
        return 0
    fi
    
    # Check for domain references in .htaccess
    local has_domain_refs
    has_domain_refs=$(docker compose exec -T wp grep "$OLD_DOMAIN" "$htaccess_path" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    
    if [[ "$has_domain_refs" -eq 0 ]]; then
        log INFO "No domain references found in .htaccess, skipping"
        return 0
    fi
    
    log INFO "Found $has_domain_refs domain reference(s) in .htaccess, updating..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "[DRY RUN] Would update .htaccess domain references: $OLD_DOMAIN -> $NEW_DOMAIN"
        return 0
    fi
    
    # Copy .htaccess from container for editing
    if ! docker compose exec -T wp cp "$htaccess_path" "$temp_htaccess"; then
        log ERROR "Failed to copy .htaccess for editing"
        return 1
    fi
    
    # Replace domain references (avoid double replacement)
    docker compose exec -T wp sed -i "s|\\b$OLD_DOMAIN\\b|$NEW_DOMAIN|g" "$temp_htaccess"
    
    # Copy updated .htaccess back
    if ! docker compose exec -T wp cp "$temp_htaccess" "$htaccess_path"; then
        log ERROR "Failed to update .htaccess"
        return 1
    fi
    
    # Clean up temporary file
    docker compose exec -T wp rm -f "$temp_htaccess" || true
    
    log INFO ".htaccess updated successfully"
    return 0
}

update_filesystem() {
    log INFO "Starting file system updates"
    
    update_wp_config
    update_htaccess
    
    # Check for any theme or plugin files with domain references
    log INFO "Searching for additional domain references in WordPress files"
    
    local file_refs
    file_refs=$(docker compose exec -T wp find /var/www/html -type f \( -name "*.php" -o -name "*.css" -o -name "*.js" \) -exec grep -l "$OLD_DOMAIN" {} \; 2>/dev/null | head -10 || echo "")
    
    if [[ -n "$file_refs" ]]; then
        # Separate cache files from other files
        local cache_files non_cache_files
        cache_files=$(echo "$file_refs" | grep -E "(cache|tmp)" || echo "")
        non_cache_files=$(echo "$file_refs" | grep -v -E "(cache|tmp)" || echo "")
        
        if [[ -n "$cache_files" ]]; then
            log WARN "Found cache files with domain references (will be cleared):"
            echo "$cache_files" | while read -r file; do
                if [[ -n "$file" ]]; then
                    log WARN "  - $file"
                fi
            done
            
            if [[ "$DRY_RUN" != "true" ]]; then
                log INFO "Clearing cache files with domain references"
                echo "$cache_files" | while read -r file; do
                    if [[ -n "$file" ]]; then
                        docker compose exec -T wp rm -f "$file" || log WARN "Failed to remove cache file $file"
                    fi
                done
                
                # Clear OptimizePress cache specifically
                log INFO "Clearing OptimizePress cache directories"
                docker compose exec -T wp rm -rf /var/www/html/wp-content/plugins/op-builder/public/assets/cache/* 2>/dev/null || true
                docker compose exec -T wp rm -rf /var/www/html/wp-content/uploads/op-cache/* 2>/dev/null || true
            else
                log INFO "[DRY RUN] Would clear cache files instead of editing them"
            fi
        fi
        
        if [[ -n "$non_cache_files" ]]; then
            log WARN "Found non-cache files with domain references:"
            echo "$non_cache_files" | while read -r file; do
                if [[ -n "$file" ]]; then
                    log WARN "  - $file"
                fi
            done
            log WARN "These files may need manual review and updating"
            
            if [[ "$DRY_RUN" != "true" ]] && [[ "$FORCE" != "true" ]]; then
                echo -n "Would you like to automatically replace domain references in these non-cache files? [y/N]: "
                read -r response
                if [[ "$response" =~ ^[Yy]$ ]]; then
                    log INFO "Updating non-cache files with domain references"
                    echo "$non_cache_files" | while read -r file; do
                        if [[ -n "$file" ]]; then
                            docker compose exec -T wp sed -i "s|\\b$OLD_DOMAIN\\b|$NEW_DOMAIN|g" "$file" || log WARN "Failed to update $file"
                        fi
                    done
                fi
            fi
        fi
    else
        log INFO "No additional domain references found in theme/plugin files"
    fi
    
    log INFO "File system updates completed"
    return 0
}

# Docker environment update functions
update_docker_environment() {
    log INFO "Starting Docker environment updates"
    
    # Load current environment variables
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        # shellcheck disable=SC1091
        source "$PROJECT_ROOT/.env"
    else
        log ERROR ".env file not found for Docker environment updates"
        return 1
    fi
    
    local env_file="$PROJECT_ROOT/.env"
    local backup_env="${BACKUP_DIR}/.env.backup-$(date +%Y%m%d_%H%M%S)"
    
    log INFO "Checking .env file for domain references"
    local has_server_name has_virtual_host has_letsencrypt_host has_warmup_url has_domain_refs
    has_server_name=$(grep "^SERVER_NAME=" "$env_file" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    has_virtual_host=$(grep "^VIRTUAL_HOST=" "$env_file" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0") 
    has_letsencrypt_host=$(grep "^LETSENCRYPT_HOST=" "$env_file" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    has_warmup_url=$(grep "^WARMUP_URL=" "$env_file" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    has_domain_refs=$(grep "$OLD_DOMAIN" "$env_file" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    
    if [[ "$has_domain_refs" -eq 0 ]]; then
        log INFO "No domain references found in .env file, skipping Docker environment updates"
        rm -f "$backup_env"
        return 0
    fi
    
    log INFO "Found domain references in .env file, updating..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "[DRY RUN] Would update .env file with new domain:"
        if [[ "$has_server_name" -gt 0 ]]; then
            log INFO "[DRY RUN] - Update SERVER_NAME: $OLD_DOMAIN -> $NEW_DOMAIN"
        fi
        if [[ "$has_virtual_host" -gt 0 ]]; then
            log INFO "[DRY RUN] - Update VIRTUAL_HOST: $OLD_DOMAIN -> $NEW_DOMAIN"
        fi
        if [[ "$has_letsencrypt_host" -gt 0 ]]; then
            log INFO "[DRY RUN] - Update LETSENCRYPT_HOST: $OLD_DOMAIN -> $NEW_DOMAIN"
        fi
        if [[ "$has_warmup_url" -gt 0 ]]; then
            log INFO "[DRY RUN] - Update WARMUP_URL domain references"
        fi
        log INFO "[DRY RUN] - Replace any other domain references in .env"
        log INFO "[DRY RUN] Would restart Docker containers with new environment"
        return 0
    fi
    
    # Create backup of .env file
    log INFO "Creating backup of .env file"
    if ! cp "$env_file" "$backup_env"; then
        log ERROR "Failed to backup .env file"
        return 1
    fi
    
    # Update .env file with new domain (avoid double replacement)
    log INFO "Updating .env file domain references"
    sed -i "s|\\b$OLD_DOMAIN\\b|$NEW_DOMAIN|g" "$env_file"
    
    # Verify .env file was updated correctly
    local new_domain_count
    new_domain_count=$(grep "$NEW_DOMAIN" "$env_file" 2>/dev/null | wc -l | tr -d '\n\r' || echo "0")
    
    if [[ "$new_domain_count" -eq 0 ]]; then
        log ERROR ".env file update verification failed - restoring backup"
        cp "$backup_env" "$env_file"
        return 1
    fi
    
    log INFO ".env file updated successfully ($new_domain_count references to new domain)"
    
    # Clean up old SSL certificates from nginx-proxy
    log INFO "Cleaning up old SSL certificates for $OLD_DOMAIN"
    
    # Check if nginx-proxy container exists and is running
    if docker ps --format "table {{.Names}}" | grep -q "nginx-proxy"; then
        # Remove old certificates
        docker exec nginx-proxy rm -f "/etc/nginx/certs/${OLD_DOMAIN}.crt" 2>/dev/null || true
        docker exec nginx-proxy rm -f "/etc/nginx/certs/${OLD_DOMAIN}.key" 2>/dev/null || true  
        docker exec nginx-proxy rm -f "/etc/nginx/certs/${OLD_DOMAIN}.chain.pem" 2>/dev/null || true
        docker exec nginx-proxy rm -f "/etc/nginx/certs/${OLD_DOMAIN}.dhparam.pem" 2>/dev/null || true
        log DEBUG "Old SSL certificates removed"
    else
        log WARN "nginx-proxy container not found, skipping SSL certificate cleanup"
    fi
    
    return 0
}

restart_containers() {
    log INFO "Restarting containers with updated environment"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "[DRY RUN] Would restart Docker containers to apply new domain configuration"
        return 0
    fi
    
    # Stop containers gracefully
    log INFO "Stopping containers..."
    if ! docker compose down; then
        log ERROR "Failed to stop containers"
        return 1
    fi
    
    # Start containers with new environment
    log INFO "Starting containers with new configuration..."
    if ! docker compose up -d; then
        log ERROR "Failed to start containers with new configuration"
        return 1
    fi
    
    # Wait for containers to be healthy
    log INFO "Waiting for containers to become healthy..."
    local max_wait=60
    local wait_count=0
    
    while [[ $wait_count -lt $max_wait ]]; do
        if docker compose ps --services --filter "status=running" | grep -q "wp" && \
           docker compose ps --services --filter "status=running" | grep -q "db"; then
            log INFO "Containers are running successfully"
            break
        fi
        sleep 2
        ((wait_count+=2))
    done
    
    if [[ $wait_count -ge $max_wait ]]; then
        log ERROR "Containers failed to start properly within ${max_wait}s"
        return 1
    fi
    
    # Verify nginx-proxy network connectivity
    if docker network ls | grep -q "nginx-proxy"; then
        local compose_name="${COMPOSE_PROJECT_NAME:-wordpress}"
        local wp_container="wp-${compose_name}"
        
        # Give nginx-proxy time to detect the new container
        sleep 5
        
        if docker network inspect nginx-proxy --format '{{range .Containers}}{{.Name}} {{end}}' | grep -q "$wp_container"; then
            log INFO "Container successfully connected to nginx-proxy network"
        else
            log WARN "Container may not be properly connected to nginx-proxy network"
        fi
    fi
    
    return 0
}

# Cache clearing functions
clear_caches() {
    log INFO "Starting cache clearing operations"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log INFO "[DRY RUN] Would clear all caches:"
        log INFO "[DRY RUN] - WordPress object cache (Redis)"
        log INFO "[DRY RUN] - FrankenWP/Caddy cache"
        log INFO "[DRY RUN] - WordPress transients"
        return 0
    fi
    
    # Clear WordPress object cache (Redis)
    log INFO "Clearing WordPress object cache"
    if docker compose ps --services --filter "status=running" | grep -q "redis"; then
        # Clear Redis cache
        $DOCKER_REDIS redis-cli FLUSHALL || log WARN "Failed to clear Redis cache"
        log DEBUG "Redis cache cleared"
    else
        log DEBUG "Redis container not running, skipping Redis cache clear"
    fi
    
    # Clear WordPress transients via WP-CLI
    log INFO "Clearing WordPress transients"
    $WP_CLI transient delete --all || log WARN "Failed to clear WordPress transients"
    
    # Clear FrankenWP/Caddy cache
    log INFO "Clearing FrankenWP cache"
    
    # Load environment for PURGE_KEY
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        # shellcheck disable=SC1091
        source "$PROJECT_ROOT/.env"
    fi
    
    # Clear cache using purge key if available
    if [[ -n "${PURGE_KEY:-}" ]]; then
        log DEBUG "Using PURGE_KEY to clear FrankenWP cache"
        local target_protocol="https"
        local current_siteurl
        current_siteurl=$($WP_CLI option get siteurl 2>/dev/null || echo "")
        
        if [[ "$current_siteurl" == http://* ]]; then
            target_protocol="http"
        fi
        
        local purge_url="${target_protocol}://${NEW_DOMAIN}/*"
        
        # Attempt to purge cache via HTTP request
        if command -v curl &> /dev/null; then
            curl -X PURGE -H "Cache-Control: no-cache" -H "X-Purge-Key: ${PURGE_KEY}" "$purge_url" || log WARN "Cache purge via HTTP failed"
        fi
    fi
    
    # Clear cache files from container if accessible
    local cache_loc="${CACHE_LOC:-/tmp/cache}"
    docker compose exec -T wp find "$cache_loc" -type f -name "*.cache" -delete 2>/dev/null || log DEBUG "No cache files found to delete"
    
    # Clear WordPress cache via WP-CLI
    log INFO "Flushing WordPress cache"
    $WP_CLI cache flush || log WARN "Failed to flush WordPress cache"
    
    log INFO "Cache clearing completed"
    return 0
}

# Post-migration verification functions
verify_migration() {
    log INFO "Starting post-migration verification"
    
    local verification_failed=0
    
    # Test database integrity
    log INFO "Verifying database integrity"
    local current_home current_siteurl
    current_home=$($WP_CLI option get home 2>/dev/null || echo "")
    current_siteurl=$($WP_CLI option get siteurl 2>/dev/null || echo "")
    
    if [[ -z "$current_home" ]] || [[ -z "$current_siteurl" ]]; then
        log ERROR "Cannot retrieve WordPress URLs from database"
        ((verification_failed++))
    elif [[ "$current_home" != *"$NEW_DOMAIN"* ]] || [[ "$current_siteurl" != *"$NEW_DOMAIN"* ]]; then
        log ERROR "WordPress URLs not updated correctly"
        log ERROR "Home URL: $current_home"
        log ERROR "Site URL: $current_siteurl"
        ((verification_failed++))
    else
        log INFO "Database URLs verified successfully"
        log INFO "Home URL: $current_home"
        log INFO "Site URL: $current_siteurl"
    fi
    
    # Test database connection
    if ! $DOCKER_DB mysql -u "${MYSQL_USER:-wordpress}" -p"${MYSQL_PASSWORD}" -e "SELECT 1" "${MYSQL_DATABASE:-wordpress}" &> /dev/null; then
        log ERROR "Database connection test failed"
        ((verification_failed++))
    else
        log INFO "Database connection verified"
    fi
    
    # Test WordPress admin access
    log INFO "Testing WordPress admin accessibility"
    local admin_url="${current_siteurl}/wp-admin/"
    if command -v curl &> /dev/null; then
        local response_code
        response_code=$(curl -s -o /dev/null -w "%{http_code}" -L --max-time 10 "$admin_url" || echo "000")
        if [[ "$response_code" =~ ^[23] ]]; then
            log INFO "WordPress admin accessible (HTTP $response_code)"
        else
            log WARN "WordPress admin returned HTTP $response_code"
            # Don't fail verification for this as it might be due to redirects or auth
        fi
    else
        log DEBUG "curl not available, skipping admin access test"
    fi
    
    # Test frontend access
    log INFO "Testing frontend accessibility"
    if command -v curl &> /dev/null; then
        local response_code
        response_code=$(curl -s -o /dev/null -w "%{http_code}" -L --max-time 10 "$current_home" || echo "000")
        if [[ "$response_code" =~ ^[23] ]]; then
            log INFO "Frontend accessible (HTTP $response_code)"
        else
            log WARN "Frontend returned HTTP $response_code"
            # Don't fail verification for this as it might be due to DNS or SSL setup
        fi
    else
        log DEBUG "curl not available, skipping frontend access test"
    fi
    
    # Verify container health
    log INFO "Verifying container health"
    if ! docker compose ps --services --filter "status=running" | grep -q "wp"; then
        log ERROR "WordPress container is not running"
        ((verification_failed++))
    elif ! docker compose ps --services --filter "status=running" | grep -q "db"; then
        log ERROR "Database container is not running" 
        ((verification_failed++))
    else
        log INFO "Containers are running successfully"
    fi
    
    # Test WP-CLI functionality
    if ! $WP_CLI --info &> /dev/null; then
        log ERROR "WP-CLI is not responding"
        ((verification_failed++))
    else
        log INFO "WP-CLI verified working"
    fi
    
    # Check file permissions
    log INFO "Checking WordPress file permissions"
    local wp_content_writable
    wp_content_writable=$(docker compose exec -T wp test -w /var/www/html/wp-content && echo "yes" || echo "no")
    if [[ "$wp_content_writable" == "yes" ]]; then
        log INFO "wp-content directory is writable"
    else
        log WARN "wp-content directory may not be writable"
    fi
    
    # Final verification summary
    if [[ $verification_failed -eq 0 ]]; then
        log INFO "Post-migration verification completed successfully"
        return 0
    else
        log ERROR "Post-migration verification failed ($verification_failed critical issues)"
        return 1
    fi
}

# Rollback functions
rollback_migration() {
    log WARN "Starting migration rollback"
    
    if [[ -z "${BACKUP_DB_FILE:-}" ]] || [[ -z "${BACKUP_FILES_FILE:-}" ]]; then
        log ERROR "Backup files not available for rollback"
        log ERROR "Manual recovery required using emergency methods"
        return 1
    fi
    
    if [[ ! -f "$BACKUP_DB_FILE" ]] || [[ ! -f "$BACKUP_FILES_FILE" ]]; then
        log ERROR "Backup files missing: $BACKUP_DB_FILE, $BACKUP_FILES_FILE"
        log ERROR "Manual recovery required"
        return 1
    fi
    
    log INFO "Rolling back database from: $(basename "$BACKUP_DB_FILE")"
    log INFO "Rolling back files from: $(basename "$BACKUP_FILES_FILE")"
    
    # Load environment variables
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        # shellcheck disable=SC1091
        source "$PROJECT_ROOT/.env"
    fi
    
    local db_name="${MYSQL_DATABASE:-wordpress}"
    
    # Restore database
    if [[ -f "$PROJECT_ROOT/bin/restore-database.sh" ]]; then
        log INFO "Restoring database using existing script"
        if ! "$PROJECT_ROOT/bin/restore-database.sh" "$db_name" "$BACKUP_DB_FILE"; then
            log ERROR "Database rollback failed"
            return 1
        fi
    else
        log INFO "Restoring database using mysql command"
        if ! $DOCKER_DB mysql \
            -u "${MYSQL_USER:-wordpress}" \
            -p"${MYSQL_PASSWORD}" \
            "$db_name" < "$BACKUP_DB_FILE"; then
            log ERROR "Database rollback failed"
            return 1
        fi
    fi
    
    # Restore files
    log INFO "Restoring WordPress files"
    if ! docker compose exec -T wp rm -rf /var/www/html/*; then
        log ERROR "Failed to clear current WordPress files"
        return 1
    fi
    
    if ! docker compose cp "$BACKUP_FILES_FILE" wp:/tmp/restore-backup.tar.gz; then
        log ERROR "Failed to copy files backup to container"
        return 1
    fi
    
    if ! docker compose exec -T wp tar -xzf /tmp/restore-backup.tar.gz -C /var/www/html; then
        log ERROR "Failed to extract files backup"
        return 1
    fi
    
    # Clean up temporary file
    docker compose exec -T wp rm -f /tmp/restore-backup.tar.gz || true
    
    # Restore .env file if backup exists
    local env_backup
    env_backup=$(find "$PROJECT_ROOT" -name ".env.backup-*" -type f | sort -r | head -1)
    if [[ -n "$env_backup" ]]; then
        log INFO "Restoring .env file from backup"
        cp "$env_backup" "$PROJECT_ROOT/.env"
    fi
    
    # Restart containers with restored environment
    log INFO "Restarting containers with restored configuration"
    docker compose down || true
    sleep 2
    if ! docker compose up -d; then
        log ERROR "Failed to restart containers after rollback"
        return 1
    fi
    
    # Clear caches
    sleep 5  # Wait for containers to be ready
    log INFO "Clearing caches after rollback"
    $WP_CLI cache flush || true
    $DOCKER_REDIS redis-cli FLUSHALL || true
    
    log INFO "Migration rollback completed"
    log WARN "Please verify that your site is working correctly"
    
    return 0
}

show_emergency_recovery() {
    log INFO "Emergency Recovery Options:"
    echo ""
    echo "If the migration fails and the site becomes inaccessible, try these methods:"
    echo ""
    echo "1. WordPress RELOCATE constant (automatic URL detection):"
    echo "   docker compose exec wp wp config set RELOCATE true"
    echo "   # Then access wp-login.php - WordPress will auto-detect URLs"
    echo "   docker compose exec wp wp config delete RELOCATE"
    echo ""
    echo "2. Manual wp-config.php URL override:"
    echo "   docker compose exec wp wp config set WP_HOME 'https://$OLD_DOMAIN'"
    echo "   docker compose exec wp wp config set WP_SITEURL 'https://$OLD_DOMAIN'"
    echo ""
    echo "3. Direct WP-CLI URL update:"
    echo "   docker compose exec wp wp option update home 'https://$OLD_DOMAIN'"
    echo "   docker compose exec wp wp option update siteurl 'https://$OLD_DOMAIN'"
    echo ""
    echo "4. Database restoration:"
    if [[ -n "${BACKUP_DB_FILE:-}" ]]; then
        echo "   ./bin/restore-database.sh ${MYSQL_DATABASE:-wordpress} '$BACKUP_DB_FILE'"
    else
        echo "   Use your most recent database backup with restore-database.sh"
    fi
    echo ""
    echo "5. Complete rollback (if backup files available):"
    echo "   $0 --rollback"
    echo ""
}

# Main execution with error handling and rollback capability
main() {
    parse_arguments "$@"
    initialize
    
    # Handle rollback request
    if [[ "${1:-}" == "--rollback" ]]; then
        log WARN "Rollback mode requested"
        if rollback_migration; then
            log INFO "Rollback completed successfully"
            exit 0
        else
            log ERROR "Rollback failed"
            show_emergency_recovery
            exit 1
        fi
    fi
    
    # Main migration flow
    if ! verify_environment; then
        exit 1
    fi
    
    if ! create_backup; then
        log ERROR "Backup creation failed - aborting migration"
        exit 1
    fi
    
    # Show final confirmation unless in dry-run or force mode
    if [[ "$DRY_RUN" != "true" ]] && [[ "$FORCE" != "true" ]]; then
        echo ""
        log INFO "Ready to migrate WordPress from '$OLD_DOMAIN' to '$NEW_DOMAIN'"
        log INFO "Backup created: Database and files backed up"
        echo ""
        echo -n "Proceed with migration? [y/N]: "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log INFO "Migration cancelled by user"
            exit 0
        fi
    fi
    
    # Execute migration steps
    local migration_steps=(
        "migrate_database"
        "update_filesystem"
        "update_docker_environment"
        "restart_containers"
        "clear_caches"
    )
    
    local failed_step=""
    for step in "${migration_steps[@]}"; do
        if ! $step; then
            failed_step="$step"
            break
        fi
    done
    
    if [[ -n "$failed_step" ]]; then
        log ERROR "Migration failed at step: $failed_step"
        
        if [[ "$DRY_RUN" != "true" ]]; then
            echo ""
            echo -n "Would you like to attempt rollback? [y/N]: "
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                if rollback_migration; then
                    log INFO "Rollback completed - your site should be restored"
                else
                    log ERROR "Rollback also failed"
                    show_emergency_recovery
                fi
            else
                show_emergency_recovery
            fi
        fi
        exit 1
    fi
    
    # Verify successful migration
    if [[ "$DRY_RUN" != "true" ]]; then
        if verify_migration; then
            log INFO "🎉 WordPress domain migration completed successfully!"
            log INFO "Your site has been migrated from '$OLD_DOMAIN' to '$NEW_DOMAIN'"
            log INFO "Please test your site functionality thoroughly"
            
            # Clean up backup files after successful migration (optional)
            echo ""
            echo -n "Keep backup files? [Y/n]: "
            read -r response
            if [[ "$response" =~ ^[Nn]$ ]]; then
                if [[ -n "${BACKUP_DB_FILE:-}" ]] && [[ -f "$BACKUP_DB_FILE" ]]; then
                    rm -f "$BACKUP_DB_FILE"
                    log INFO "Database backup removed"
                fi
                if [[ -n "${BACKUP_FILES_FILE:-}" ]] && [[ -f "$BACKUP_FILES_FILE" ]]; then
                    rm -f "$BACKUP_FILES_FILE"
                    log INFO "Files backup removed"
                fi
            else
                log INFO "Backup files preserved for safety"
            fi
        else
            log ERROR "Migration completed but verification failed"
            log WARN "Please check the issues above and test your site manually"
            log INFO "Rollback available with: $0 --rollback"
        fi
    else
        log INFO "DRY RUN completed - no changes were made"
        log INFO "Run without --dry-run to perform the actual migration"
    fi
}

# Add rollback option to help
if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
    show_help
    echo "ROLLBACK:"
    echo "    $0 --rollback       Rollback the last migration using backups"
    echo ""
    exit 0
fi

# Execute main function with all arguments
main "$@"
