#!/usr/bin/env bash

if [ $# -ne 2 ]
  then
    echo "Usage:"
    echo "rm-old-backups.sh <backups_directory> <retention_days>"
    exit 1
fi

directory=$1
retention_days=$2
current_date=$(date +%Y%m%d%H%M%S)
age_limit=$((retention_days * 24 * 60 * 60))

# Loop through each file in the directory
for file in "$directory"/*
do
    # Extract the timestamp from the filename
    filename=$(basename "$file")
    file_timestamp=${filename:0:14}

    # Convert both timestamps to seconds since 1970-01-01
    file_seconds=$(date -d "${file_timestamp:0:4}-${file_timestamp:4:2}-${file_timestamp:6:2} ${file_timestamp:8:2}:${file_timestamp:10:2}:${file_timestamp:12:2}" +%s)
    current_seconds=$(date -d "${current_date:0:4}-${current_date:4:2}-${current_date:6:2} ${current_date:8:2}:${current_date:10:2}:${current_date:12:2}" +%s)

    # Calculate the age of the file
    age=$((current_seconds - file_seconds))

    # Check if the file is older than the age limit
    if [ $age -gt $age_limit ]; then
        echo "Deleting $file"
        rm "$file"
    fi
done
