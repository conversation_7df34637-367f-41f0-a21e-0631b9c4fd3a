# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Dockerized WordPress development environment using FrankenWP (FrankenPHP + Caddy), MariaDB, Redis, and PhpMyAdmin. The setup uses FrankenWP's built-in caching and high-performance server architecture.

## Architecture

The project consists of 4 main Docker services:
- **wp**: WordPress with FrankenPHP server and built-in Caddy cache-handler
- **db**: MariaDB 10.5 database
- **redis**: Redis cache server with password authentication
- **pma**: PhpMyAdmin (optional, activated with profile)

## Essential Commands

### Environment Setup
```bash
# Copy environment template and configure
cp .env.example .env
# Edit .env with your configuration

# Start all services
docker compose up -d

# Start with PhpMy<PERSON>d<PERSON>
docker compose --profile pma up -d

# View logs
docker compose logs -f [service_name]

# Stop services
docker compose down
```

### Database Operations
```bash
# Dump database
./bin/dump-database.sh [--batch] <database_name> <output_file>

# Restore database  
./bin/restore-database.sh <database_name> <dump_file>

# Search database
./bin/search-database.sh <search_term>
```

### Backup Operations
```bash
# Backup WordPress files (now uses frankenwp container)
./bin/backup-wp-dir.sh <backup_filename.tar.gz>

# Create full backup (database + files)
./bin/make-backup.sh

# Remove old backups
./bin/rm-old-backups.sh
```

### WordPress Management
```bash
# Access FrankenWP container
docker compose exec wp bash

# Run WP-CLI commands
docker compose exec wp wp --help
docker compose exec wp wp plugin list
docker compose exec wp wp cache flush
```

## Key Configuration Files

### Environment Variables (.env)
- `COMPOSE_PROJECT_NAME`: Project identifier used in container names
- `SERVER_NAME`: Primary domain for the site (replaces NGINX_SERVER_NAME)
- `CADDY_GLOBAL_OPTIONS`: Global Caddy server configuration options
- `CACHE_LOC`: FrankenWP cache storage location (default: /tmp/cache)
- `TTL`: Cache object storage duration in seconds (default: 3600)
- `PURGE_KEY`: Optional cache purge authentication key
- `WORDPRESS_CONFIG_EXTRA`: Additional WordPress configuration
- `FORCE_HTTPS`: Enforce HTTPS (default: true)
- Database credentials and backup settings

### Docker Configuration
- `docker-compose.yml`: Main service definitions using FrankenWP image
- `docker/redis/Dockerfile`: Redis configuration
- `docker/phpmyadmin/Dockerfile`: PhpMyAdmin configuration
- FrankenWP uses stephenmiracle/frankenwp:latest image with built-in optimizations

## Cache Management

The setup uses FrankenWP's built-in "sidekick" caching system:
- Cache path: `/tmp/cache` (configurable via CACHE_LOC)
- TTL: 1 hour by default (configurable via TTL)
- Cache purging available with PURGE_KEY authentication
- High-performance caching built into FrankenPHP/Caddy

## Cron Jobs

Automated tasks run via Ofelia scheduler:
- WordPress cron: Every 5 minutes
- Custom cron jobs can be added via `cron.txt`
- Cache warmup is no longer needed due to FrankenWP's efficient built-in caching

## Development Notes

- WordPress files are in Docker volume `wordpress`
- Database data persists in Docker volume `db`  
- FrankenWP cache persists in Docker volume `frankenwp-cache`
- All services connect via `nginx-proxy` external network
- PhpMyAdmin available when using `--profile pma`
- Redis available for object caching at `redis-${COMPOSE_PROJECT_NAME}:6379`
- FrankenWP provides better performance than traditional nginx+php-fpm setup

## Backup Strategy

The project includes comprehensive backup scripts:
- Database dumps with stored procedures (`-R` flag)
- WordPress directory compression
- GPG encryption support
- Remote backup via rclone
- Automatic old backup cleanup
- Configurable retention periods

## Network Configuration

- External network: `nginx-proxy` (must exist)
- Internal network: `default` (created automatically)  
- Services expose ports only internally except via nginx-proxy
- Nginx and PhpMyAdmin connect to external nginx-proxy network

## Claude Code Integration

### Context7 MCP Server
This project has access to the context7 MCP server for retrieving up-to-date documentation and code examples for libraries:

- Use `resolve-library-id` to find Context7-compatible library IDs for packages
- Use `get-library-docs` to fetch current documentation for specific libraries
- Helpful for WordPress development, Docker configuration, and related technologies
- Example: Get latest WordPress hooks documentation or Docker Compose best practices

When working with unfamiliar libraries or needing current documentation, leverage the context7 server for accurate, up-to-date information.