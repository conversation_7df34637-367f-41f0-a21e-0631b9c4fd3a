<?php
// disable root login
$cfg['Servers'][1]['AllowRoot'] = getenv('ALLOW_ROOT') === 'true';

/**
 * Default value: 'invisible'
 * Valid values: 'invisible', 'checkbox'
 */
getenv('CAPTCHA_METHOD') && $cfg['CaptchaMethod'] = getenv('CAPTCHA_METHOD');

/**
 * The URL for the reCaptcha v2 service’s API, either Google’s or a compatible one.
 * Default value: 'https://www.google.com/recaptcha/api.js'
 */
getenv('CAPTCHA_API') && $cfg['CaptchaApi'] = getenv('CAPTCHA_API');

/**
 * The Content-Security-Policy snippet (URLs from which to allow embedded content) for the reCaptcha v2 service,
 * either Google’s or a compatible one.
 * Default value: 'https://apis.google.com https://www.google.com/recaptcha/ https://www.gstatic.com/recaptcha/ https://ssl.gstatic.com/'
 */
getenv('CAPTCHA_CSP') && $cfg['CaptchaCsp'] = getenv('CAPTCHA_CSP');

/**
 * The request parameter used for the reCaptcha v2 service.
 * Default value: 'g-recaptcha-response'
 */
getenv('CAPTCHA_REQUEST_PARAM') && $cfg['CaptchaRequestParam'] = getenv('CAPTCHA_REQUEST_PARAM');

/**
 * The response parameter used for the reCaptcha v2 service.
 * Default value: 'g-recaptcha-response'
 */
getenv('CAPTCHA_RESPONSE_PARAM') && $cfg['CaptchaResponseParam'] = getenv('CAPTCHA_RESPONSE_PARAM');

/**
 * The public key for the reCaptcha service that can be obtained from the “Admin Console” on https://www.google.com/recaptcha/about/.
 * Default value: ''
 */
getenv('CAPTCHA_LOGIN_PUBLIC_KEY') && $cfg['CaptchaLoginPublicKey'] = getenv('CAPTCHA_LOGIN_PUBLIC_KEY');

/**
 * The private key for the reCaptcha service that can be obtained from the “Admin Console” on https://www.google.com/recaptcha/about/.
 * Default value: ''
 */
getenv('CAPTCHA_LOGIN_PRIVATE_KEY') && $cfg['CaptchaLoginPrivateKey'] = getenv('CAPTCHA_LOGIN_PRIVATE_KEY');

/**
 * The URL for the reCaptcha service to do siteverify action. reCaptcha will be then used in Cookie authentication mode.
 * Default value: ''
 */
getenv('CAPTCHA_SITE_VERIFY_URL') && $cfg['CaptchaSiteVerifyURL'] = getenv('CAPTCHA_SITE_VERIFY_URL');
