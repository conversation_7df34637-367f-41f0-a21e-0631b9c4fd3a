FROM wordpress:6-php8.2-fpm-alpine AS base

RUN deluser www-data \
    && addgroup -g 101 -S www-data \
    && adduser -u 101 -D -S -G www-data www-data \
    # install packages
    && apk add --no-cache sudo wget nano pcre-dev $PHPIZE_DEPS \
    # install wp-cli
    && cd /tmp \
    && curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar \
    && chmod +x wp-cli.phar \
    && mv wp-cli.phar /usr/local/bin/wp \
    # install redis
    && pecl install redis \
    && docker-php-ext-enable redis.so \
    # cleanup
    && rm -rf /tmp/* \
    && apk del pcre $PHPIZE_DEPS

COPY docker/wordpress/php/* /usr/local/etc/php/conf.d/
COPY docker/wordpress/php-fpm/* /usr/local/etc/php-fpm.d/
COPY docker/wordpress/warmup-cache.sh /usr/local/bin/warmup-cache.sh


