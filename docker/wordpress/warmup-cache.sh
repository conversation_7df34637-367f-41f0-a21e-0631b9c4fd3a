#!/usr/bin/env bash

# Create a temporary directory for the sitemap
TEMP_DIR=$(mktemp -d)
LOG_FILE="/var/www/html/warmup-cache.log"

echo "Starting cache warmup at $(date)" > $LOG_FILE

# Function to extract URLs from a sitemap
extract_urls_from_sitemap() {
  local sitemap_url="$1"
  local output_file="$2"
  
  # Download the sitemap
  /usr/bin/wget -q -O "$TEMP_DIR/temp_sitemap.xml" "$sitemap_url"
  
  # Check if it's a sitemap index (contains <sitemapindex>)
  if grep -q "<sitemapindex" "$TEMP_DIR/temp_sitemap.xml"; then
    echo "Found sitemap index at $sitemap_url" >> $LOG_FILE
    
    # Extract sub-sitemap URLs
    grep -o '<loc>[^<]*</loc>' "$TEMP_DIR/temp_sitemap.xml" | sed 's/<loc>\(.*\)<\/loc>/\1/' > "$TEMP_DIR/sub_sitemaps.txt"
    
    # Process each sub-sitemap
    while read sub_sitemap; do
      echo "Processing sub-sitemap: $sub_sitemap" >> $LOG_FILE
      extract_urls_from_sitemap "$sub_sitemap" "$output_file"
    done < "$TEMP_DIR/sub_sitemaps.txt"
  else
    # It's a regular sitemap, extract page URLs
    echo "Processing regular sitemap: $sitemap_url" >> $LOG_FILE
    grep -o '<loc>[^<]*</loc>' "$TEMP_DIR/temp_sitemap.xml" | sed 's/<loc>\(.*\)<\/loc>/\1/' >> "$output_file"
  fi
}

# First, try to get the main sitemap
extract_urls_from_sitemap "$1/sitemap.xml" "$TEMP_DIR/urls.txt"

# If no URLs found, try sitemap_index.xml
if [ ! -s "$TEMP_DIR/urls.txt" ]; then
  extract_urls_from_sitemap "$1/sitemap_index.xml" "$TEMP_DIR/urls.txt"
fi

# If still no URLs found, fallback to crawling
if [ ! -s "$TEMP_DIR/urls.txt" ]; then
  echo "No sitemap found, falling back to crawling" >> $LOG_FILE
  /usr/bin/wget -q -r -l 2 --spider "$1" 2>&1 | grep '^--' | awk '{ print $3 }' | grep -v '\.\(css\|js\|jpg\|jpeg\|gif\|png\|ico\|svg\|webp\|woff\|woff2\|ttf\|eot\|mp4\|webm\|ogg\|mp3\|pdf\|zip\|tar\|gz\)$' > "$TEMP_DIR/urls.txt"
fi

# Filter out non-HTML URLs
grep -v '\.\(css\|js\|jpg\|jpeg\|gif\|png\|ico\|svg\|webp\|woff\|woff2\|ttf\|eot\|mp4\|webm\|ogg\|mp3\|pdf\|zip\|tar\|gz\)$' "$TEMP_DIR/urls.txt" > "$TEMP_DIR/filtered_urls.txt"
mv "$TEMP_DIR/filtered_urls.txt" "$TEMP_DIR/urls.txt"

# Process each URL and check for X-FastCGI-Cache: MISS
echo "Found $(wc -l < $TEMP_DIR/urls.txt) URLs to check" >> $LOG_FILE
WARMED_COUNT=0

while read url; do
  # Check the cache status header using a GET request that follows redirects
  # Get the last X-FastCGI-Cache header value in case of multiple redirects
  CACHE_STATUS=$(/usr/bin/curl -s -L -D - -o /dev/null -X GET "$url" | grep -i "X-FastCGI-Cache" | tail -1 | awk '{print $2}' | tr -d '[:space:]')
  
  if [ "$CACHE_STATUS" = "MISS" ]; then
    echo "Warming: $url (Cache: MISS)" >> $LOG_FILE
    # Actually fetch the page to warm the cache (already done by the check above)
    WARMED_COUNT=$((WARMED_COUNT+1))
  else
    echo "Skipping: $url (Cache: $CACHE_STATUS)" >> $LOG_FILE
  fi
done < "$TEMP_DIR/urls.txt"

echo "Cache warmup completed at $(date). Warmed up $WARMED_COUNT pages." >> $LOG_FILE

# Clean up
rm -rf $TEMP_DIR

exit 0
