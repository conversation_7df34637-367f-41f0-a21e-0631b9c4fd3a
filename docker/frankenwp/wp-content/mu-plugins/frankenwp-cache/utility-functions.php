<?php
/**
 * FrankenWP Cache Utility Functions
 * 
 * @package FrankenWP_Cache
 */

if (!defined('ABSPATH')) {
    exit;
}

// Force URL rewrite for FrankenPHP
add_filter('got_url_rewrite', function() { 
    return true; 
});

// Add cache-related functions that can be used by themes or other plugins

/**
 * Manually purge cache for a specific URL
 * 
 * @param string $url The URL to purge
 * @return bool True on success, false on failure (for backward compatibility)
 */
function frankenwp_cache_purge_url($url) {
    $path = str_replace(home_url(), '', $url);
    $result = FrankenWP_Cache_Purge::purge_path($path);
    return $result['success'] ?? false;
}

/**
 * Manually purge all cache
 * 
 * @return bool True on success, false on failure (for backward compatibility)
 */
function frankenwp_cache_purge_all() {
    $result = FrankenWP_Cache_Purge::purge_all();
    return $result['success'] ?? false;
}

/**
 * Get cache configuration status
 * 
 * @return array Configuration status information
 */
function frankenwp_cache_get_status() {
    return FrankenWP_Cache_Purge::get_status();
}

/**
 * Check if cache purging is properly configured
 * 
 * @return bool True if configured, false otherwise
 */
function frankenwp_cache_is_configured() {
    $status = FrankenWP_Cache_Purge::get_status();
    return $status['configured'];
}