<?php
/**
 * <PERSON>enWP Cache Admin Interface
 * 
 * @package FrankenWP_Cache
 */

if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'frankenwp_cache_add_admin_menu');

function frankenwp_cache_add_admin_menu() {
    add_management_page(
        'FrankenWP Cache',
        'FrankenWP Cache',
        'manage_options',
        'frankenwp-cache',
        'frankenwp_cache_admin_page'
    );
}

// Admin page content
function frankenwp_cache_admin_page() {
    $status = FrankenWP_Cache_Purge::get_status();
    
    // Handle form submission
    if (isset($_POST['purge_all_cache']) && check_admin_referer('frankenwp_cache_action', 'frankenwp_cache_nonce')) {
        $result = FrankenWP_Cache_Purge::purge_all();
        
        if ($result['success']) {
            add_settings_error(
                'frankenwp_cache_messages',
                'frankenwp_cache_message',
                $result['message'],
                'success'
            );
            
            if (isset($result['data']['purge_type'])) {
                add_settings_error(
                    'frankenwp_cache_messages',
                    'frankenwp_cache_info',
                    'Purge type: ' . $result['data']['purge_type'],
                    'info'
                );
            }
        } else {
            add_settings_error(
                'frankenwp_cache_messages',
                'frankenwp_cache_error',
                $result['message'],
                'error'
            );
            
            if (isset($result['error'])) {
                add_settings_error(
                    'frankenwp_cache_messages',
                    'frankenwp_cache_error_detail',
                    'Error: ' . $result['error'],
                    'error'
                );
            }
            
            if (isset($result['response_code'])) {
                add_settings_error(
                    'frankenwp_cache_messages',
                    'frankenwp_cache_response_code',
                    'Response code: ' . $result['response_code'],
                    'error'
                );
            }
        }
    }
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
        
        <?php settings_errors('frankenwp_cache_messages'); ?>
        
        <!-- Security Warning -->
        <?php if ($status['key_recommended']): ?>
            <div class="notice notice-warning">
                <p>
                    <strong>Security Warning:</strong> 
                    No PURGE_KEY is configured. While the cache system will work, 
                    it's <strong>strongly recommended</strong> to set a secure PURGE_KEY 
                    environment variable to prevent unauthorized cache purging.
                </p>
            </div>
        <?php endif; ?>
        
        <div class="metabox-holder">
            <div class="postbox-container" style="width: 70%;">
                <div class="meta-box-sortables">
                    
                    <!-- Cache Actions -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Cache Actions</h2>
                        </div>
                        <div class="inside">
                            <form method="post" action="">
                                <?php wp_nonce_field('frankenwp_cache_action', 'frankenwp_cache_nonce'); ?>
                                
                                <table class="form-table" role="presentation">
                                    <tr>
                                        <th scope="row">
                                            <label for="purge_all_cache">Purge All Cache</label>
                                        </th>
                                        <td>
                                            <p class="description">
                                                This will remove all cached content from the FrankenWP cache system. 
                                                The cache will be rebuilt automatically as pages are visited.
                                            </p>
                                            <?php submit_button(
                                                'Purge All Cache',
                                                'primary',
                                                'purge_all_cache',
                                                false,
                                                [
                                                    'onclick' => 'return confirm(\'Are you sure you want to purge all cache? This action cannot be undone.\')'
                                                ]
                                            ); ?>
                                        </td>
                                    </tr>
                                </table>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Configuration Status -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Configuration Status</h2>
                        </div>
                        <div class="inside">
                            <table class="form-table" role="presentation">
                                <tr>
                                    <th scope="row">Cache System</th>
                                    <td>
                                        <span class="dashicons <?php echo $status['configured'] ? 'dashicons-yes-alt' : 'dashicons-dismiss'; ?>" 
                                              style="color: <?php echo $status['configured'] ? '#46b450' : '#dc3232'; ?>"></span>
                                        <strong><?php echo $status['configured'] ? 'Ready' : 'Not Configured'; ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Security Key</th>
                                    <td>
                                        <span class="dashicons <?php echo $status['has_key'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>" 
                                              style="color: <?php echo $status['has_key'] ? '#46b450' : '#ffba00'; ?>"></span>
                                        <?php if ($status['has_key']): ?>
                                            <strong>Configured</strong>
                                            <p class="description">PURGE_KEY is set and cache operations are secured.</p>
                                        <?php else: ?>
                                            <strong>Not Set (Warning)</strong>
                                            <p class="description">
                                                PURGE_KEY environment variable is not configured. 
                                                Cache operations will work but are not secured.
                                            </p>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Purge URL Override</th>
                                    <td>
                                        <span class="dashicons <?php echo $status['purge_url_set'] ? 'dashicons-yes-alt' : 'dashicons-info'; ?>" 
                                              style="color: <?php echo $status['purge_url_set'] ? '#46b450' : '#72aee6'; ?>"></span>
                                        <?php if ($status['purge_url_set']): ?>
                                            <strong>Custom URL Configured</strong>
                                            <p class="description">Using custom PURGE_URL for cache operations.</p>
                                        <?php else: ?>
                                            <strong>Using Site URL (Default)</strong>
                                            <p class="description">Cache operations will use the site URL.</p>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Base URL</th>
                                    <td>
                                        <code><?php echo esc_html($status['base_url']); ?></code>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">Purge Endpoint</th>
                                    <td>
                                        <code><?php echo esc_html($status['base_url'] . $status['purge_path']); ?></code>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="postbox-container" style="width: 29%; margin-left: 1%;">
                <div class="meta-box-sortables">
                    
                    <!-- Environment Variables -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Environment Variables</h2>
                        </div>
                        <div class="inside">
                            <p>Configure these in your <code>.env</code> file:</p>
                            
                            <h4>Required (Recommended)</h4>
                            <ul>
                                <li><strong>PURGE_KEY</strong><br>
                                    <small>Authentication key for cache purging security</small>
                                </li>
                            </ul>
                            
                            <h4>Optional</h4>
                            <ul>
                                <li><strong>PURGE_PATH</strong><br>
                                    <small>Cache purge endpoint path<br>
                                    Default: <code>/__cache/purge</code></small>
                                </li>
                                <li><strong>PURGE_URL</strong><br>
                                    <small>Base URL for cache purging<br>
                                    Useful for reverse proxy setups</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Reverse Proxy Setup -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Reverse Proxy Setup</h2>
                        </div>
                        <div class="inside">
                            <p>If your site is behind a reverse proxy (like nginx-proxy), set <code>PURGE_URL</code> to the internal container URL:</p>
                            <pre style="background: #f6f7f7; padding: 10px; border-radius: 3px; font-size: 12px; overflow-x: auto;"><code>PURGE_URL=http://frankenwp-container-name</code></pre>
                            <p><small>This ensures cache purge requests are sent directly to the FrankenWP container instead of going through the proxy.</small></p>
                        </div>
                    </div>
                    
                    <!-- Performance Tips -->
                    <div class="postbox">
                        <div class="postbox-header">
                            <h2 class="hndle">Performance Tips</h2>
                        </div>
                        <div class="inside">
                            <ul>
                                <li><strong>Automatic Purging:</strong> Cache is automatically purged when content is updated</li>
                                <li><strong>Selective Purging:</strong> Only affected pages are purged, not the entire cache</li>
                                <li><strong>Built-in Optimization:</strong> FrankenWP includes advanced caching optimizations</li>
                            </ul>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <style>
        .form-table th {
            width: 200px;
        }
        .postbox-container {
            float: left;
        }
        .notice.notice-info {
            border-left-color: #72aee6;
        }
        .notice.notice-info p {
            color: #1d2327;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
    <?php
}

// Add admin bar link for quick access
add_action('admin_bar_menu', 'frankenwp_cache_add_admin_bar', 999);

function frankenwp_cache_add_admin_bar($wp_admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $wp_admin_bar->add_node([
        'id' => 'frankenwp-purge-cache',
        'title' => 'Purge Cache',
        'href' => admin_url('tools.php?page=frankenwp-cache'),
        'meta' => [
            'title' => 'FrankenWP Cache Management'
        ]
    ]);
}

// AJAX handler for quick purge
add_action('wp_ajax_frankenwp_purge_cache', 'frankenwp_cache_ajax_purge');

function frankenwp_cache_ajax_purge() {
    check_ajax_referer('frankenwp_cache_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    $result = FrankenWP_Cache_Purge::purge_all();
    
    wp_send_json([
        'success' => $result['success'],
        'message' => $result['message'],
        'data' => $result['data'] ?? null,
        'error' => $result['error'] ?? null,
        'response_code' => $result['response_code'] ?? null
    ]);
}