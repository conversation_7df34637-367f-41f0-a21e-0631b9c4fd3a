<?php
/**
 * FrankenWP Cache Purge Class
 * 
 * Shared functionality for cache purging operations with Caddy cache-handler
 * <PERSON>les reverse proxy scenarios with PURGE_URL configuration
 * 
 * @package FrankenWP_Cache
 * @version 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FrankenWP_Cache_Purge {
    
    /**
     * Get the base URL for cache purge operations
     * 
     * @return string The base URL to use for purge requests
     */
    private static function get_purge_base_url() {
        $purge_url = getenv('PURGE_URL');
        
        if (!empty($purge_url)) {
            return rtrim($purge_url, '/');
        }
        
        // Fallback to site URL if PURGE_URL is not set
        return get_site_url();
    }
    
    /**
     * Get purge configuration
     * 
     * @return array Configuration array with path and key
     */
    private static function get_purge_config() {
        return [
            'path' => getenv('PURGE_PATH') ?: '/__cache/purge',
            'key' => getenv('PURGE_KEY') ?: '',
            'base_url' => self::get_purge_base_url()
        ];
    }
    
    /**
     * Purge all cache
     * 
     * @return array Result array with success status and message
     */
    public static function purge_all() {
        $config = self::get_purge_config();
        $url = $config['base_url'] . $config['path'] . '/';
        
        return self::send_purge_request($url, $config['key'], 'all');
    }
    
    /**
     * Purge specific content by path
     * 
     * @param string $path The content path to purge
     * @return array Result array with success status and message
     */
    public static function purge_path($path) {
        $config = self::get_purge_config();
        $url = $config['base_url'] . $config['path'] . '/' . ltrim($path, '/');
        
        return self::send_purge_request($url, $config['key'], $path);
    }
    
    /**
     * Send purge request to Caddy cache-handler
     * 
     * @param string $url The purge URL
     * @param string $key The purge key for authentication
     * @param string $target Target description for logging
     * @return array Result array with success status and message
     */
    private static function send_purge_request($url, $key, $target = '') {
        // Debug logging
        error_log("FrankenWP Cache purge attempt - Target: $target, URL: $url, Key: " . ($key ? 'SET' : 'EMPTY'));
        
        $response = wp_remote_post($url, [
            'headers' => [
                'X-Cache-Purge-Key' => $key,
                'Content-Type' => 'application/json',
            ],
            'timeout' => 30,
            'sslverify' => false, // Allow self-signed certificates in development
        ]);
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log("FrankenWP Cache purge error for $target: " . $error_message);
            return [
                'success' => false,
                'message' => "Request failed: $error_message",
                'response_code' => 0
            ];
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        // Try to decode JSON response
        $json_response = json_decode($response_body, true);
        
        if ($json_response && is_array($json_response)) {
            // Handle structured JSON response
            error_log("FrankenWP Cache purge response for $target - Code: $response_code, Success: " . 
                     ($json_response['success'] ? 'true' : 'false') . ", Message: " . 
                     ($json_response['message'] ?? 'No message'));
            
            return [
                'success' => $json_response['success'] ?? false,
                'message' => $json_response['message'] ?? 'Unknown response',
                'response_code' => $response_code,
                'data' => $json_response['data'] ?? null,
                'error' => $json_response['error'] ?? null
            ];
        } else {
            // Handle plain text response (legacy)
            error_log("FrankenWP Cache purge response for $target - Code: $response_code, Body: $response_body");
            
            return [
                'success' => $response_code === 200,
                'message' => $response_code === 200 ? 'Cache purged successfully' : 'Cache purge failed',
                'response_code' => $response_code,
                'body' => $response_body
            ];
        }
    }
    
    /**
     * Get cache status and configuration info
     * 
     * @return array Status information
     */
    public static function get_status() {
        $config = self::get_purge_config();
        
        return [
            'configured' => true, // Always available, just less secure without key
            'purge_url_set' => !empty(getenv('PURGE_URL')),
            'base_url' => $config['base_url'],
            'purge_path' => $config['path'],
            'has_key' => !empty($config['key']),
            'key_recommended' => empty($config['key']) // Warning when no key is set
        ];
    }
}