<?php
/**
 * FrankenWP Cache Auto-Purge Hooks
 * 
 * Automatically purges cache when content is updated
 * 
 * @package FrankenWP_Cache
 */

if (!defined('ABSPATH')) {
    exit;
}

// Purge cache when posts are saved/updated
add_action('save_post', 'frankenwp_cache_purge_on_save_post', 10, 1);
add_action('delete_post', 'frankenwp_cache_purge_on_delete_post', 10, 1);

function frankenwp_cache_purge_on_save_post($post_id) {
    // Skip autosaves and revisions
    if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
        return;
    }
    
    $post = get_post($post_id);
    if (!$post) {
        return;
    }
    
    // Only purge published posts
    if ($post->post_status !== 'publish') {
        return;
    }
    
    // Purge the specific post
    $post_path = str_replace(home_url(), '', get_permalink($post_id));
    FrankenWP_Cache_Purge::purge_path($post_path);
    
    // Also purge homepage and any archive pages for this post type
    frankenwp_cache_purge_related_pages($post);
}

function frankenwp_cache_purge_on_delete_post($post_id) {
    $post = get_post($post_id);
    if (!$post) {
        return;
    }
    
    // Purge the specific post
    $post_path = str_replace(home_url(), '', get_permalink($post_id));
    FrankenWP_Cache_Purge::purge_path($post_path);
    
    // Also purge homepage and any archive pages for this post type
    frankenwp_cache_purge_related_pages($post);
}

function frankenwp_cache_purge_related_pages($post) {
    // Purge homepage
    FrankenWP_Cache_Purge::purge_path('/');
    
    // Purge category and tag archives for posts
    if ($post->post_type === 'post') {
        $categories = get_the_category($post->ID);
        foreach ($categories as $category) {
            $category_link = get_category_link($category->term_id);
            $category_path = str_replace(home_url(), '', $category_link);
            FrankenWP_Cache_Purge::purge_path($category_path);
        }
        
        $tags = get_the_tags($post->ID);
        if ($tags) {
            foreach ($tags as $tag) {
                $tag_link = get_tag_link($tag->term_id);
                $tag_path = str_replace(home_url(), '', $tag_link);
                FrankenWP_Cache_Purge::purge_path($tag_path);
            }
        }
    }
    
    // Purge post type archive
    if (get_post_type_archive_link($post->post_type)) {
        $archive_link = get_post_type_archive_link($post->post_type);
        $archive_path = str_replace(home_url(), '', $archive_link);
        FrankenWP_Cache_Purge::purge_path($archive_path);
    }
}

// Purge cache when comments are approved/updated
add_action('comment_post', 'frankenwp_cache_purge_on_comment', 10, 1);
add_action('edit_comment', 'frankenwp_cache_purge_on_comment', 10, 1);
add_action('delete_comment', 'frankenwp_cache_purge_on_comment', 10, 1);
add_action('trash_comment', 'frankenwp_cache_purge_on_comment', 10, 1);
add_action('untrash_comment', 'frankenwp_cache_purge_on_comment', 10, 1);
add_action('spam_comment', 'frankenwp_cache_purge_on_comment', 10, 1);
add_action('unspam_comment', 'frankenwp_cache_purge_on_comment', 10, 1);

function frankenwp_cache_purge_on_comment($comment_id) {
    $comment = get_comment($comment_id);
    if (!$comment) {
        return;
    }
    
    // Purge the post that this comment belongs to
    $post_path = str_replace(home_url(), '', get_permalink($comment->comment_post_ID));
    FrankenWP_Cache_Purge::purge_path($post_path);
}

// Purge cache when themes/plugins are updated
add_action('switch_theme', 'frankenwp_cache_purge_all_on_theme_change');
add_action('activated_plugin', 'frankenwp_cache_purge_all_on_plugin_change');
add_action('deactivated_plugin', 'frankenwp_cache_purge_all_on_plugin_change');

function frankenwp_cache_purge_all_on_theme_change() {
    FrankenWP_Cache_Purge::purge_all();
}

function frankenwp_cache_purge_all_on_plugin_change() {
    FrankenWP_Cache_Purge::purge_all();
}

// Purge cache when customizer settings are saved
add_action('customize_save_after', 'frankenwp_cache_purge_all_on_customizer_save');

function frankenwp_cache_purge_all_on_customizer_save() {
    FrankenWP_Cache_Purge::purge_all();
}