<?php
/**
 * Plugin Name: FrankenWP Cache Management
 * Description: Complete cache management for FrankenWP with Caddy cache-handler integration
 * Version: 2.0.0
 * Author: FrankenWP Team
 * 
 * Environment Variables:
 * - PURGE_URL: Base URL for cache purging (optional, defaults to site URL)
 * - PURGE_PATH: Cache purge endpoint path (default: /__cache/purge)
 * - PURGE_KEY: Authentication key for cache purging (X-Cache-Purge-Key header)
 */

if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('FRANKENWP_CACHE_PLUGIN_DIR', __DIR__ . '/frankenwp-cache/');
define('FRANKENWP_CACHE_PLUGIN_URL', plugin_dir_url(__FILE__) . 'frankenwp-cache/');

// Load the cache purge class
require_once FRANKENWP_CACHE_PLUGIN_DIR . 'class-cache-purge.php';

// Load admin interface
require_once FRANKENWP_CACHE_PLUGIN_DIR . 'admin-interface.php';

// Load automatic purging hooks
require_once FRANKENWP_CACHE_PLUGIN_DIR . 'auto-purge-hooks.php';

// Load utility functions
require_once FRANKENWP_CACHE_PLUGIN_DIR . 'utility-functions.php';