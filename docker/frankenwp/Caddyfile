{
	local_certs
	auto_https disable_redirects # This is required to avoid redirect loops when using a reverse proxy and cloud load balancers.
	{$CADDY_GLOBAL_OPTIONS}

	# https://caddyserver.com/docs/caddyfile/directives#sorting-algorithm
    order php_server before file_server
    order php before file_server
    order cache before rewrite
    order request_header before cache

	frankenphp {
		#worker /path/to/your/worker.php
		{$FRANKENPHP_CONFIG}
	}

	cache {
        api {
            souin
        }
        key {
            template {http.request.method}-{http.request.host}-{http.request.uri.path}-{http.request.uri.query}
        }
        ttl {$CACHE_TTL:24h}
        stale {$CACHE_STALE:1h}
        badger {
            configuration {
                Dir /var/cache/badger
                ValueDir /var/cache/badger
            }
        }
        {$CACHE_EXTRA_CONFIG}
    }

    log default {
        output stderr
        format console
        include http.log.access admin.api
    }
}

{$CADDY_EXTRA_CONFIG}

## Need to set all hosts with port for the cloud.
# You may not have the hostname being called due to dynamic IPs and load balancers.
# Allowing all hosts on port 80 for health checks, local dev & cases where the hostname is unknown.
{$SERVER_NAME:localhost} {

    root * /var/www/html/
    encode br zstd gzip
    log

	# Handle static files
	@documents {
		file
		path *.pdf *.doc *.docx *.xls *.xlsx *.ppt *.pptx *.txt *.csv *.rtf *.odt *.ods *.odp
	}
	handle @documents {
	    header Pragma public
    	header Cache-Control "public, max-age=31536000"
	}

	@videos {
		file
		path *.mp4 *.avi *.mov *.wmv *.flv *.mkv *.webm
	}
	handle @videos {
	    header Pragma public
    	header Cache-Control "public, max-age=31536000"
	}

	@audio {
		file
		path *.mp3 *.wav *.ogg *.aac
	}
	handle @audio {
	    header Pragma public
    	header Cache-Control "public, max-age=31536000"
	}

	# Handle favicon.ico without caching
	handle /favicon.ico {
		file_server {
			disable_canonical_uris
		}
	}
	
	# Handle robots.txt without caching
	handle /robots.txt {
		file_server {
			disable_canonical_uris
		}
	}
	
	# Deny access to hidden files (except .well-known)
	@hidden {
		path_regexp hidden `^/\.`
		not path /.well-known/*
	}
	handle @hidden {
		respond "Access denied" 403
	}
	
	# Deny PHP files in uploads directory
	@uploads_php {
		path_regexp uploads `^/(?:uploads|files)/.*\.php$`
	}
	handle @uploads_php {
		respond "Access denied" 403
	}
	
	# Disable XML-RPC
	handle /xmlrpc.php {
		respond "Access denied" 403
	}
	
	# Protect wp-config.php
	handle /wp-config.php {
		respond "Access denied" 403
	}
	
	# Protect WooCommerce upload folder
	@woocommerce_uploads {
		path /wp-content/uploads/woocommerce_uploads/*
	}
	handle @woocommerce_uploads {
		respond "Access denied" 403
	}

	# WebP image support
    @webpImages {
        path *.png *.jpg *.jpeg *.gif *.svg *.ico
    }
	
	# WebP image handling with fallback
	handle @webpImages {
		header Vary Accept
		header Pragma public
		header Cache-Control "public, max-age=31536000"
		@webp_support {
			header Accept *webp*
		}
		# Try WebP version first if browser supports it
		try_files {path}.webp {path} =404
		file_server {
			disable_canonical_uris
		}
	}

    @cache-matcher {
        # Cookie-based bypass patterns
        not header_regexp Cookie "comment_author|wordpress_[a-f0-9]+|wp-postpass|wordpress_logged_in"
        # WordPress admin and system paths
        not path_regexp "(/xmlrpc.php|/wp-.*.php)"
        not path_regexp "(/feed/|wp-comments-popup.php|wp-links-opml.php|wp-locations.php)"
        not path_regexp "(sitemap(index)?.xml|[a-z0-9-]+-sitemap([0-9]+)?.xml)"
        # E-commerce and funnels
        not path_regexp "(/(cart|checkout|my-account|koszyk|zamowienie|moje-konto|panel-partnera|step)/)"
        not path_regexp "(/funnel/[^?]*$)"
        # Images
        not path *.png *.jpg *.jpeg *.gif *.webp *.svg *.ico
        # Documents
        not path *.pdf *.doc *.docx *.xls *.xlsx *.ppt *.pptx *.txt *.csv *.rtf *.odt *.ods *.odp
        # Videos
        not path *.mp4 *.avi *.mov *.wmv *.flv *.mkv *.webm
        # Audio
        not path *.mp3 *.wav *.ogg *.aac
        {$CACHE_EXTRA_MATCHES}
    }

    cache @cache-matcher
    php_server

	{$CADDY_SERVER_EXTRA_DIRECTIVES}
}
