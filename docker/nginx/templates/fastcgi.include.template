#fastcgi_cache purge
location ~ /purge(/.*) {
   fastcgi_cache_purge phpcache "$scheme$request_method$host$1";
}

#fastcgi_cache start
set $skip_cache 0;

# Create a combined condition for /funnel/ URLs without query string
set $funnel_query "";
if ($request_uri ~* "^/funnel/") {
    set $funnel_query "${funnel_query}F";
}
if ($query_string != "") {
    set $funnel_query "${funnel_query}Q";
}
if ($funnel_query = "F") {
    set $skip_cache 1;
}

# WooCommerce
if ($request_uri ~* "^/(cart|checkout|my-account)/") {
    set $skip_cache 1;
}

if ($request_uri ~* "^/(koszyk|zamowienie|moje-konto|panel-partnera)/") {
    set $skip_cache 1;
}

# CartFlows
if ($request_uri ~* "^/step/") {
    set $skip_cache 1;
}

# POST requests should always go to PHP
if ($request_method = POST) {
    set $skip_cache 1;
}

#if ($query_string != "") {
#    set $skip_cache 1;
#}

# Don't cache uris containing the following segments
if ($request_uri ~* "/wp-admin/|/xmlrpc.php|wp-.*.php|^/feed/*|/tag/.*/feed/*|index.php|/.*sitemap.*\.(xml|xsl)") {
    set $skip_cache 1;
}

# Don't use the cache for logged in users or recent commenters
if ($http_cookie ~* "comment_author|wordpress_[a-f0-9]+|wp-postpass|wordpress_no_cache|wordpress_logged_in") {
    set $skip_cache 1;
}

location / {
    try_files $uri $uri/ /index.php?$args;
}

location ~ [^/]\.php(/|$) {
    try_files $uri =404;
    include fastcgi_params;
    fastcgi_buffers 16 16k;
    fastcgi_buffer_size 32k;
    fastcgi_pass  ${FASTCGI_PASS};
    fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
    proxy_send_timeout 300;
    proxy_read_timeout 300;
    fastcgi_send_timeout 300;
    fastcgi_read_timeout 300;

    fastcgi_cache_bypass $skip_cache;
    fastcgi_no_cache $skip_cache;
    fastcgi_cache phpcache;
    fastcgi_cache_valid 200 301 302 1d;
    fastcgi_cache_use_stale error timeout updating invalid_header http_500 http_503;
    fastcgi_cache_min_uses 1;
    fastcgi_cache_lock on;
    add_header X-FastCGI-Cache $upstream_cache_status;
}

location ~* .(ogg|ogv|svg|svgz|eot|otf|woff|woff2|mp4|ttf|css|rss|atom|js|jpg|jpeg|gif|png|ico|zip|tgz|gz|rar|bz2|doc|xls|exe|ppt|tar|mid|midi|wav|bmp|rtf|webp)$ {
    expires 1y;
    add_header Pragma public;
    add_header Cache-Control "public";
    log_not_found off;
    access_log off;
}
