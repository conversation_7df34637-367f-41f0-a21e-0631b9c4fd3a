# default Docker DNS server
resolver 127.0.0.11;

# Basic Settings
tcp_nopush on;
tcp_nodelay on;
types_hash_max_size 2048;
server_tokens off;
client_max_body_size 64m;

# Gzip Settings
gzip on;
gzip_disable "msie6";
gzip_vary on;
gzip_proxied any;
gzip_comp_level 6;
gzip_buffers 16 8k;
gzip_http_version 1.1;
gzip_min_length 256;
gzip_types
    application/atom+xml
    application/geo+json
    application/javascript
    application/json
    application/ld+json
    application/manifest+json
    application/rdf+xml
    application/rss+xml
    application/vnd.geo+json
    application/vnd.ms-fontobject
    application/x-font-ttf
    application/x-javascript
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/eot
    font/opentype
    font/otf
    font/ttf
    image/bmp
    image/svg+xml
    image/x-icon
    text/cache-manifest
    text/css
    text/javascript
    text/plain
    text/vcard
    text/vnd.rim.location.xloc
    text/vtt
    text/x-component
    text/x-cross-domain-policy
    text/xml;


# Fast CGI cache configuration
fastcgi_cache_path /var/cache/nginx/fastcgi levels=1:2 keys_zone=phpcache:500m max_size=10g inactive=2d use_temp_path=off;
fastcgi_cache_key "$scheme$request_method$host$request_uri";
fastcgi_ignore_headers Cache-Control Expires Set-Cookie;

# WebP image support
map $http_accept $webp_suffix {
  default "";
  "~*webp" ".webp";
}

server {
    listen 80 default_server;
    server_name ${NGINX_SERVER_NAME};
    root /var/www/html;
    large_client_header_buffers 4 32k;

    index index.php index.html index.htm;

    # WebP image support
    location ~* ^.+\.(png|jpe?g|gif)$ {
      add_header Vary Accept;
      expires 1y;
      add_header Pragma public;
      add_header Cache-Control "public";
      log_not_found off;
      access_log off;
      try_files $uri$webp_suffix $uri =404;
    }

    include conf.d/restrictions.include;

    include conf.d/fastcgi.include;
}
