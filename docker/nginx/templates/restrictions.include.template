# Global restrictions configuration file.
# Designed to be included in any server {} block.

location = /favicon.ico {
    log_not_found off;
    access_log off;
}

location = /robots.txt {
    allow all;
    log_not_found off;
    access_log off;
}

# Deny all attempts to access hidden files such as .htaccess, .htpasswd, .DS_Store (Mac).
# Keep logging the requests to parse later (or to pass to firewall utilities such as fail2ban)
location ~ /\.(?!well-known).* {
    deny all;
}

# Deny access to any files with a .php extension in the uploads directory
# Works in sub-directory installs and also in multisite network
# Keep logging the requests to parse later (or to pass to firewall utilities such as fail2ban)
location ~* /(?:uploads|files)/.*\.php$ {
    deny all;
}

# Disable access to XML-RPC
location ~* ^/xmlrpc.php$ {
    return 403;
}

# Disable access to wp-config.php
location = /wp-config.php {
    deny all;
}

# Protect WooCommerce upload folder from being accessed directly.
location ~* /wp-content/uploads/woocommerce_uploads/ {
    if ( $upstream_http_x_accel_redirect = "" ) {
        return 403;
    }
    internal;
}

